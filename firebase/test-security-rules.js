#!/usr/bin/env node
/**
 * Firebase Security Rules Testing Script
 * Tests Firestore and Storage security rules for all environments
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const fs = require('fs');

// Test configuration
const PROJECT_ID = 'recruiva-dev-test';
const FIRESTORE_RULES = fs.readFileSync('./dev/firestore.rules', 'utf8');
const STORAGE_RULES = fs.readFileSync('./dev/storage.rules', 'utf8');

let testEnv;

async function setupTestEnvironment() {
  testEnv = await initializeTestEnvironment({
    projectId: PROJECT_ID,
    firestore: {
      rules: FIRESTORE_RULES,
    },
    storage: {
      rules: STORAGE_RULES,
    },
  });
}

async function testFirestoreRules() {
  console.log('🔥 Testing Firestore Security Rules...');
  
  // Test authenticated user access
  const authenticatedUser = testEnv.authenticatedContext('user1', {
    email: '<EMAIL>',
    companyId: 'company1'
  });
  
  // Test unauthenticated access
  const unauthenticatedUser = testEnv.unauthenticatedContext();
  
  // Test 1: Authenticated user can access their company data
  console.log('✓ Testing authenticated company access...');
  await assertSucceeds(
    authenticatedUser.firestore()
      .collection('companies')
      .doc('company1')
      .get()
  );
  
  // Test 2: Authenticated user cannot access other company data
  console.log('✓ Testing cross-company access restriction...');
  await assertFails(
    authenticatedUser.firestore()
      .collection('companies')
      .doc('company2')
      .get()
  );
  
  // Test 3: Unauthenticated user cannot access company data
  console.log('✓ Testing unauthenticated access restriction...');
  await assertFails(
    unauthenticatedUser.firestore()
      .collection('companies')
      .doc('company1')
      .get()
  );
  
  // Test 4: Development user can access test collections
  const devUser = testEnv.authenticatedContext('dev1', {
    email: '<EMAIL>'
  });
  
  console.log('✓ Testing development user access...');
  await assertSucceeds(
    devUser.firestore()
      .collection('test')
      .doc('test1')
      .set({ data: 'test' })
  );
  
  // Test 5: Public interview sessions are readable
  console.log('✓ Testing public interview session access...');
  await assertSucceeds(
    unauthenticatedUser.firestore()
      .collection('public_interview_sessions')
      .doc('session1')
      .get()
  );
  
  console.log('✅ All Firestore rule tests passed!');
}

async function testStorageRules() {
  console.log('🗄️ Testing Storage Security Rules...');
  
  const authenticatedUser = testEnv.authenticatedContext('user1', {
    email: '<EMAIL>',
    companyId: 'company1'
  });
  
  const unauthenticatedUser = testEnv.unauthenticatedContext();
  
  // Test 1: Authenticated user can upload to their company folder
  console.log('✓ Testing company file upload...');
  await assertSucceeds(
    authenticatedUser.storage()
      .ref('companies/company1/document.pdf')
      .put(Buffer.from('test'))
  );
  
  // Test 2: User cannot upload to other company folders
  console.log('✓ Testing cross-company upload restriction...');
  await assertFails(
    authenticatedUser.storage()
      .ref('companies/company2/document.pdf')
      .put(Buffer.from('test'))
  );
  
  // Test 3: Public files are readable by anyone
  console.log('✓ Testing public file access...');
  await assertSucceeds(
    unauthenticatedUser.storage()
      .ref('public/logo.png')
      .getDownloadURL()
  );
  
  console.log('✅ All Storage rule tests passed!');
}

async function runTests() {
  try {
    await setupTestEnvironment();
    await testFirestoreRules();
    await testStorageRules();
    
    console.log('\n🎉 All security rule tests passed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    if (testEnv) {
      await testEnv.cleanup();
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
