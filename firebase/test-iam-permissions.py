#!/usr/bin/env python3
"""
Test script to verify IAM permissions and service account access
"""

import os
import sys
import json
from google.cloud import firestore
from google.cloud import storage
import firebase_admin
from firebase_admin import credentials, auth

def test_service_account_access(project_id, service_account_path):
    """Test service account access to Firebase services"""
    print(f"🔑 Testing service account access for {project_id}...")
    
    try:
        # Initialize Firebase Admin SDK
        if not firebase_admin._apps:
            cred = credentials.Certificate(service_account_path)
            firebase_admin.initialize_app(cred, {
                'projectId': project_id,
                'storageBucket': f'{project_id}.appspot.com'
            })
        
        # Test Firestore access
        print("✓ Testing Firestore access...")
        db = firestore.Client(project=project_id)
        
        # Try to create a test document
        test_ref = db.collection('test').document('iam_test')
        test_ref.set({'test': True, 'timestamp': firestore.SERVER_TIMESTAMP})
        
        # Try to read the document
        doc = test_ref.get()
        if doc.exists:
            print("  ✅ Firestore read/write access confirmed")
        
        # Clean up test document
        test_ref.delete()
        
        # Test Storage access
        print("✓ Testing Storage access...")
        storage_client = storage.Client(project=project_id)
        bucket = storage_client.bucket(f'{project_id}.appspot.com')
        
        # Try to list objects (this tests read access)
        blobs = list(bucket.list_blobs(max_results=1))
        print("  ✅ Storage read access confirmed")
        
        # Test Auth access
        print("✓ Testing Auth access...")
        # Try to list users (this tests auth admin access)
        users = auth.list_users(max_results=1)
        print("  ✅ Auth admin access confirmed")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Access test failed: {e}")
        return False

def test_environment_isolation():
    """Test that environments are properly isolated"""
    print("\n🔒 Testing environment isolation...")
    
    environments = {
        'development': 'recruiva-dev',
        'demo': 'recruiva-demo', 
        'production': 'recruiva'
    }
    
    for env_name, project_id in environments.items():
        print(f"\n📋 Testing {env_name} environment ({project_id})...")
        
        # Check if service account key exists
        key_path = f"./dev/service-account-key.json"  # Adjust path as needed
        if os.path.exists(key_path):
            success = test_service_account_access(project_id, key_path)
            if success:
                print(f"  ✅ {env_name} environment access confirmed")
            else:
                print(f"  ❌ {env_name} environment access failed")
        else:
            print(f"  ⚠️  Service account key not found for {env_name}")

def test_cors_configuration():
    """Test CORS configuration by making requests to different environments"""
    print("\n🌐 Testing CORS configuration...")
    
    import requests
    
    cors_tests = [
        {
            'name': 'Development API',
            'url': 'https://recruiva-dev.onrender.com/api/v1/health',
            'origin': 'https://recruiva-dev.vercel.app'
        },
        {
            'name': 'Demo API', 
            'url': 'https://recruiva-demo.onrender.com/api/v1/health',
            'origin': 'https://recruiva-demo.vercel.app'
        }
    ]
    
    for test in cors_tests:
        try:
            print(f"✓ Testing {test['name']} CORS...")
            
            # Make OPTIONS request to test CORS
            response = requests.options(
                test['url'],
                headers={
                    'Origin': test['origin'],
                    'Access-Control-Request-Method': 'GET'
                },
                timeout=10
            )
            
            if response.status_code == 200:
                cors_headers = response.headers.get('Access-Control-Allow-Origin', '')
                if test['origin'] in cors_headers or '*' in cors_headers:
                    print(f"  ✅ CORS configured correctly for {test['name']}")
                else:
                    print(f"  ⚠️  CORS may not be configured for {test['origin']}")
            else:
                print(f"  ⚠️  Could not test CORS for {test['name']} (status: {response.status_code})")
                
        except requests.RequestException as e:
            print(f"  ⚠️  Could not reach {test['name']}: {e}")

def main():
    """Run all IAM and security tests"""
    print("🔐 Firebase IAM and Security Testing")
    print("=" * 50)
    
    # Test environment isolation
    test_environment_isolation()
    
    # Test CORS configuration
    test_cors_configuration()
    
    print("\n" + "=" * 50)
    print("🎯 IAM and Security Testing Complete!")
    print("\nNext steps:")
    print("1. If service account keys are missing, run the IAM setup commands")
    print("2. If CORS tests fail, verify backend deployment and environment detection")
    print("3. Deploy Firebase security rules using: firebase deploy --only firestore:rules")

if __name__ == "__main__":
    main()
