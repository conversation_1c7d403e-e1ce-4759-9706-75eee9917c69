#!/usr/bin/env python3
"""
Test multi-tenant security implementation
"""

import os
import sys
sys.path.append('../backend')

from app.services.firebase_service import FirebaseService
from app.core.config import settings

def test_multi_tenant_access():
    """Test multi-tenant data access patterns"""
    print("🏢 Testing Multi-Tenant Security...")
    
    try:
        # Initialize Firebase service
        firebase = FirebaseService()
        db = firebase.db
        
        # Test 1: Create test companies
        print("✓ Creating test companies...")
        
        company1_data = {
            'name': 'Test Company 1',
            'domain': 'testcompany1.com',
            'createdAt': firebase.get_server_timestamp(),
            'allowedUsers': ['<EMAIL>']
        }
        
        company2_data = {
            'name': 'Test Company 2', 
            'domain': 'testcompany2.com',
            'createdAt': firebase.get_server_timestamp(),
            'allowedUsers': ['<EMAIL>']
        }
        
        # Create companies
        company1_ref = db.collection('companies').document('test_company_1')
        company2_ref = db.collection('companies').document('test_company_2')
        
        company1_ref.set(company1_data)
        company2_ref.set(company2_data)
        
        print("  ✅ Test companies created")
        
        # Test 2: Create company-specific data
        print("✓ Creating company-specific test data...")
        
        # Jobs for company 1
        job1_data = {
            'title': 'Software Engineer',
            'companyId': 'test_company_1',
            'createdBy': '<EMAIL>',
            'createdAt': firebase.get_server_timestamp()
        }
        
        # Jobs for company 2
        job2_data = {
            'title': 'Product Manager',
            'companyId': 'test_company_2', 
            'createdBy': '<EMAIL>',
            'createdAt': firebase.get_server_timestamp()
        }
        
        db.collection('jobs').document('job_1').set(job1_data)
        db.collection('jobs').document('job_2').set(job2_data)
        
        print("  ✅ Company-specific jobs created")
        
        # Test 3: Verify data isolation
        print("✓ Testing data isolation...")
        
        # Query jobs for company 1
        company1_jobs = db.collection('jobs').where('companyId', '==', 'test_company_1').get()
        company1_job_count = len(list(company1_jobs))
        
        # Query jobs for company 2
        company2_jobs = db.collection('jobs').where('companyId', '==', 'test_company_2').get()
        company2_job_count = len(list(company2_jobs))
        
        print(f"  Company 1 jobs: {company1_job_count}")
        print(f"  Company 2 jobs: {company2_job_count}")
        
        if company1_job_count > 0 and company2_job_count > 0:
            print("  ✅ Data isolation working correctly")
        else:
            print("  ⚠️  Data isolation test inconclusive")
        
        # Test 4: Test dashboard collection structure
        print("✓ Testing dashboard collection structure...")
        
        dashboard1_data = {
            'companyId': 'test_company_1',
            'summary': {
                'totalJobs': 1,
                'totalApplications': 0,
                'totalCandidates': 0
            },
            'recentJobs': [{'id': 'job_1', 'title': 'Software Engineer'}],
            'lastUpdated': firebase.get_server_timestamp()
        }
        
        db.collection('dashboards').document('test_company_1').set(dashboard1_data)
        print("  ✅ Dashboard structure test passed")
        
        # Cleanup test data
        print("✓ Cleaning up test data...")
        company1_ref.delete()
        company2_ref.delete()
        db.collection('jobs').document('job_1').delete()
        db.collection('jobs').document('job_2').delete()
        db.collection('dashboards').document('test_company_1').delete()
        
        print("  ✅ Test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Multi-tenant test failed: {e}")
        return False

def test_environment_specific_behavior():
    """Test environment-specific behavior"""
    print(f"\n🌍 Testing Environment-Specific Behavior ({settings.ENVIRONMENT})...")
    
    try:
        firebase = FirebaseService()
        db = firebase.db
        
        # Test environment-specific collections
        if settings.ENVIRONMENT == 'development':
            print("✓ Testing development-specific features...")
            
            # Test access to test collection
            test_ref = db.collection('test').document('dev_test')
            test_ref.set({'environment': 'development', 'test': True})
            
            doc = test_ref.get()
            if doc.exists:
                print("  ✅ Development test collection access confirmed")
            
            test_ref.delete()
            
        elif settings.ENVIRONMENT == 'demo':
            print("✓ Testing demo-specific features...")
            
            # Test demo data collection
            demo_ref = db.collection('demo_data').document('sample')
            demo_ref.set({'type': 'sample_data', 'environment': 'demo'})
            print("  ✅ Demo data collection access confirmed")
            
        elif settings.ENVIRONMENT == 'production':
            print("✓ Testing production-specific features...")
            
            # Test system collection (should require admin access)
            try:
                system_ref = db.collection('system').document('config')
                system_ref.set({'environment': 'production'})
                print("  ⚠️  System collection access (may require admin privileges)")
            except Exception as e:
                print(f"  ✅ System collection properly restricted: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Environment test failed: {e}")
        return False

def main():
    """Run all multi-tenant security tests"""
    print("🔐 Multi-Tenant Security Testing")
    print("=" * 50)
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Firebase Project: {settings.FIREBASE_PROJECT_ID}")
    print("=" * 50)
    
    # Test multi-tenant access
    mt_success = test_multi_tenant_access()
    
    # Test environment-specific behavior
    env_success = test_environment_specific_behavior()
    
    print("\n" + "=" * 50)
    if mt_success and env_success:
        print("🎉 All multi-tenant security tests passed!")
    else:
        print("⚠️  Some tests failed - check Firebase security rules deployment")
    
    print("\nNext steps:")
    print("1. Deploy security rules: firebase deploy --only firestore:rules")
    print("2. Test with actual user authentication")
    print("3. Verify in Firebase Console security tab")

if __name__ == "__main__":
    main()
