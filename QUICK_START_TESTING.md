# Quick Start Testing Guide - Phase 1

## 🚀 Quick Test Commands

### 1. Test Backend Environment Detection (✅ Working)
```bash
cd backend
source venv/bin/activate
python test_environment.py
```

### 2. Test Docker Setup (Requires Docker Desktop)
```bash
# Make sure Docker Desktop is running first
./test-docker-setup.sh
```

### 3. Test Frontend Environment Detection
```bash
cd frontend
node test-environment.js
```

### 4. Test Full Local Development Stack
```bash
# Start both backend and frontend with Docker
docker compose -f docker-compose.dev.yml up

# Or start individually:
# Backend: docker compose -f docker-compose.dev.yml up backend
# Frontend: docker compose -f docker-compose.dev.yml up frontend
```

### 5. Test Without Docker (Traditional Development)
```bash
# Terminal 1 - Backend
cd backend
source venv/bin/activate
export ENVIRONMENT=development
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Terminal 2 - Frontend  
cd frontend
export NODE_ENV=development
export NEXT_PUBLIC_ENVIRONMENT=development
npm run dev
```

## 🔍 What to Verify

### Backend Environment Detection
- ✅ Environment detected as "development"
- ✅ Firebase project set to "recruiva-dev"
- ✅ API URL set to "https://recruiva-dev.onrender.com"
- ✅ Debug mode enabled

### Docker Setup
- 🔄 Backend container builds successfully
- 🔄 Frontend container builds successfully
- 🔄 Hot reload works when editing files
- 🔄 Services accessible on localhost:8000 and localhost:3000

### Firebase Integration
- 🔄 Connects to recruiva-dev Firebase project
- 🔄 Service account authentication works
- 🔄 Firestore client initializes successfully

## 🐛 Troubleshooting

### Docker Issues
```bash
# Check Docker status
docker info

# Check container logs
docker compose -f docker-compose.dev.yml logs backend
docker compose -f docker-compose.dev.yml logs frontend

# Rebuild containers
docker compose -f docker-compose.dev.yml build --no-cache
```

### Environment Issues
```bash
# Check environment variables
env | grep -E "(ENVIRONMENT|FIREBASE)"

# Test specific environment
ENVIRONMENT=demo python backend/test_environment.py
```

### Firebase Issues
```bash
# Test Firebase connection
cd backend && source venv/bin/activate
python -c "
from app.services.firebase_service import FirebaseService
firebase = FirebaseService()
print('Firebase connected successfully')
print(f'Project: {firebase.db._client._project}')
"
```

## ✅ Success Indicators

When everything is working correctly, you should see:

1. **Backend Environment Test**: All tests pass with development environment detected
2. **Docker Test**: Both containers build and run successfully
3. **Hot Reload**: File changes trigger automatic restarts
4. **API Access**: `curl http://localhost:8000/api/v1/health` returns success
5. **Frontend Access**: `http://localhost:3000` loads successfully
6. **Firebase**: Backend connects to recruiva-dev project

## 📞 Next Steps After Testing

1. **If all tests pass**: Proceed to deploy to development environment (Vercel + Render)
2. **If Docker tests fail**: Check Docker Desktop installation and status
3. **If Firebase tests fail**: Verify service account keys and project access
4. **If environment detection fails**: Check environment variable configuration

## 🎯 Ready for Phase 2?

Phase 1 is complete when:
- ✅ Environment detection works for all environments
- ✅ Docker development setup is functional
- ✅ Firebase integration works with correct projects
- ✅ Hot reload is working for development
- ✅ Deployment configurations are ready

Once Phase 1 testing is successful, we can proceed to Phase 2: Multi-tenant authentication system implementation.
