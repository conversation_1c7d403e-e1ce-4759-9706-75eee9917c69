# Firebase Database Design Specification - Performance Optimized

## Table of Contents

1. [Overview](#overview)
2. [Performance Optimization Summary](#performance-optimization-summary)
3. [Design Principles](#design-principles)
4. [Collection Structure](#collection-structure)
5. [Collection Specifications](#collection-specifications)
   - [companies](#companies)
   - [dashboards](#dashboards)
   - [applications](#applications)
   - [users](#users)
   - [jobs](#jobs)
   - [templates](#templates)
   - [questions](#questions)
   - [criteria](#criteria)
   - [candidates](#candidates)
   - [resumes](#resumes)
   - [resumeEvaluations](#resumeEvaluations)
   - [interviewEvaluations](#interviewEvaluations)
   - [interviews](#interviews)
   - [transcripts](#transcripts)
   - [jobPostings](#jobPostings)
6. [Performance-Critical Indexes](#performance-critical-indexes)
7. [Optimized Query Patterns](#optimized-query-patterns)
8. [Pagination Strategy](#pagination-strategy)
9. [Public Job Search Strategy](#public-job-search-strategy)
10. [Data Validation Rules](#data-validation-rules)
11. [Optimized Security Rules](#optimized-security-rules)
12. [Data Transfer Objects](#data-transfer-objects)
13. [Cloud Functions Strategy](#cloud-functions-strategy)
14. [Concurrency Control Implementation](#concurrency-control-implementation)
15. [Error Handling and Monitoring](#error-handling-and-monitoring)

## Overview

This document defines the **performance-optimized** database design for Recruiva's Firebase implementation. The design has been completely restructured to eliminate critical performance bottlenecks that would cause frontend loading delays and scaling failures.

## Design Principles

1. **Performance-First Architecture**: Every design decision optimized for query performance and scalability
2. **Pagination-Native Design**: All large collections designed for efficient pagination
3. **Strategic Denormalization**: Critical display fields denormalized to eliminate multiple lookups
4. **Compound Index Optimization**: All multi-tenant queries supported by proper indexes
5. **Size-Bounded Collections**: No unbounded arrays or documents that grow indefinitely
6. **Security Rule Efficiency**: Rules optimized to minimize database reads during authorization
7. **Real-time Cost Management**: Listeners designed for minimal data transfer

## Collection Structure

```
firebase/
├── companies
├── dashboards           # OPTIMIZED: Size-limited dashboard with 5 recent items + aggregates
├── users
├── jobs
├── templates
├── questions
├── criteria
├── applications         # OPTIMIZED: Denormalized with essential display fields
├── candidates
├── resumes
├── resumeEvaluations
├── interviewEvaluations
├── interviews
├── transcripts
└── jobPostings
```

## Collection Specifications

### companies

**Purpose**: Store company/organization information for multi-tenant user management.

**Fields**:

| Field          | Type      | Description                                | Required | Default           | Notes                                 |
| -------------- | --------- | ------------------------------------------ | -------- | ----------------- | ------------------------------------- |
| id             | string    | Unique company identifier                  | Yes      | auto-id           | Primary key                           |
| name           | string    | Company name                               | Yes      | -                 | Unique                                |
| domain         | string    | Company domain (for SSO/email)             | No       | null              |                                       |
| industry       | string    | Company industry                           | No       | null              |                                       |
| size           | string    | Company size range                         | No       | null              |                                       |
| createdAt      | timestamp | Creation timestamp                         | Yes      | serverTimestamp() |                                       |
| updatedAt      | timestamp | Last update timestamp                      | Yes      | serverTimestamp() |                                       |
| status         | string    | Company status (active, suspended)         | Yes      | "active"          |                                       |
| billingAccount | string    | Billing account number                     | No       | null              |                                       |
| address        | string    | Mailing addtress                           | Yes      | null              |                                       |
| phone          | string    | Phone number                               | Yes      | null              |                                       |
| email          | string    | Contact person (super Admin) email address | Yes      | null              | Primary contact email for the company |
| recruivaEmail  | string    | Associated @recruiva.ai email address      | No       | -                 | Unique                                |
| settings       | map       | Company preferences/settings               | No       | {}                |                                       |

**Relationships**:

- One-to-many with `users`: A company can have multiple users (Hiring Managers, Recruiters, Admins)
- One-to-many with `jobs`: A company can have multiple jobs
- One-to-many with `candidates`: A company can be associated with multiple candidates

**Common Queries**:

- Get company by ID:`companies.doc(companyId).get()`
- Get all users for a company:`users.where('companyId', '==', companyId).get()`
- Get all jobs for a company:`jobs.where('companyId', '==', companyId).get()`
- Get all applications for a company:`applications.where('companyId', '==', companyId).get()`
- Get all interviews for a company:`interviews.where('companyId', '==', companyId).get()`
- Get all interview evaluations for a company:`interviewEvaluations.where('companyId', '==', companyId).get()`
- Get total applications for a company:`applications.where('companyId', '==', companyId).count()`
- Get pass rate for a company:`interviewEvaluations.where('companyId', '==', companyId).where('result', '==', 'pass').count() / interviewEvaluations.where('companyId', '==', companyId).count()`
- Get highest scored applicants for a company:`interviewEvaluations.where('companyId', '==', companyId).orderBy('score', 'desc').limit(N).get()`
- Get interviews conducted for a company:`interviews.where('companyId', '==', companyId).count()`

### dashboards

**Purpose**: Store size-limited company dashboard data with aggregate statistics and only 5 most recent items.

**Fields**:

| Field                                            | Type      | Description                                          | Required | Default           | Notes                         |
| ------------------------------------------------ | --------- | ---------------------------------------------------- | -------- | ----------------- | ----------------------------- |
| id                                               | string    | Company identifier (document ID = companyId)         | Yes      | companyId         | Primary key                   |
| companyId                                        | string    | Reference to company                                 | Yes      | -                 | Foreign key to companies      |
| summary                                          | map       | Company-wide summary metrics                         | Yes      | -                 | **Core dashboard data** |
| summary.openJobsCount                            | number    | Jobs with status != 'Closed'                         | Yes      | 0                 |                               |
| summary.activeApplicationsCount                  | number    | Applications in progress                             | Yes      | 0                 |                               |
| summary.interviewsThisWeekCount                  | number    | Interviews this week                                 | Yes      | 0                 |                               |
| summary.finalistsCount                           | number    | Candidates in final stage with 'Go'                  | Yes      | 0                 |                               |
| summary.applicationStats                         | map       | Application statistics                               | Yes      | -                 | **Aggregate metrics**   |
| summary.applicationStats.totalApplicationsCount  | number    | Total applications count                             | Yes      | 0                 |                               |
| summary.applicationStats.pendingReviewCount      | number    | Submitted status applications                        | Yes      | 0                 |                               |
| summary.applicationStats.inInterviewCount        | number    | Interview/Evaluation status                          | Yes      | 0                 |                               |
| summary.applicationStats.resumePassCount         | number    | Resume PASS status                                   | Yes      | 0                 |                               |
| summary.applicationStats.resumeFailCount         | number    | Resume FAIL status                                   | Yes      | 0                 |                               |
| summary.applicationStats.interviewPassCount      | number    | Latest interview Go decision                         | Yes      | 0                 |                               |
| summary.applicationStats.interviewFailCount      | number    | Latest interview No Go decision                      | Yes      | 0                 |                               |
| summary.applicationStats.avgResumeScore          | number    | Average resume score                                 | Yes      | 0                 |                               |
| summary.applicationStats.avgLatestInterviewScore | number    | Average latest interview score                       | Yes      | 0                 |                               |
| summary.applicationStats.applicationsThisWeek    | number    | Applications this week                               | Yes      | 0                 |                               |
| summary.applicationStats.applicationsThisMonth   | number    | Applications this month                              | Yes      | 0                 |                               |
| recentJobs                                       | array     | **LIMITED**: Recent jobs array (max 5)         | Yes      | []                | **Size optimized**      |
| recentJobs[].id                                  | string    | Job ID                                               | Yes      | -                 |                               |
| recentJobs[].title                               | string    | Job title                                            | Yes      | -                 |                               |
| recentJobs[].team                                | string    | Team name                                            | Yes      | ""                |                               |
| recentJobs[].createdBy                           | string    | Creator user ID                                      | Yes      | -                 |                               |
| recentJobs[].createdByName                       | string    | Creator name                                         | Yes      | -                 |                               |
| recentJobs[].applicantsCount                     | number    | Number of applicants                                 | Yes      | 0                 |                               |
| recentJobs[].status                              | string    | Job status                                           | Yes      | "Intake"          |                               |
| recentJobs[].createdAt                           | timestamp | Job creation timestamp                               | Yes      | -                 | **For sorting**         |
| recentApplications                               | array     | **LIMITED**: Recent applications array (max 5) | Yes      | []                | **Size optimized**      |
| recentApplications[].id                          | string    | Application ID                                       | Yes      | -                 |                               |
| recentApplications[].candidateName               | string    | Candidate name                                       | Yes      | -                 |                               |
| recentApplications[].jobTitle                    | string    | Job title                                            | Yes      | -                 |                               |
| recentApplications[].jobId                       | string    | Job ID                                               | Yes      | -                 | **For navigation**      |
| recentApplications[].submittedAt                 | timestamp | Application submission timestamp                     | Yes      | -                 |                               |
| recentApplications[].status                      | string    | Application status                                   | Yes      | "Submitted"       |                               |
| recentApplications[].resumeScore                 | number    | Resume score                                         | No       | null              | **Quick preview**       |
| recentApplications[].latestInterviewScore        | number    | Latest interview score                               | No       | null              | **Quick preview**       |
| updatedAt                                        | timestamp | Last update timestamp                                | Yes      | serverTimestamp() |                               |
| version                                          | number    | Optimistic updates version                           | Yes      | 1                 |                               |

**Indexes**:

**Simple**:

- companyId (ASC) - Company dashboard lookup

**Compound**:

- companyId (ASC), updatedAt (DESC) - Recent updates
- companyId (ASC), version (ASC) - Optimistic concurrency control

**Relationships**:

- One-to-one with `companies`: Each company has exactly one dashboard
- Aggregates from `jobs`, `applications`, `candidates`, `resumeEvaluations`, `interviewEvaluations`

**Common Queries**:

- Get dashboard: `dashboards.doc(companyId).get()`
- Real-time updates: `dashboards.doc(companyId).onSnapshot(callback)`

**Cloud Function Updates**:

- Updated by `updateCompanyDashboard(companyId)` function
- Triggered by changes to jobs, applications, interviews, evaluations
- Maintains aggregate statistics and recent item lists

### users

**Purpose**: Store user account information, authentication details, and company association for multi-tenant support.

**Fields**:

| Field                  | Type      | Description                                            | Required | Default           | Notes                                                                      |
| :--------------------- | --------- | ------------------------------------------------------ | -------- | ----------------- | -------------------------------------------------------------------------- |
| id                     | string    | Unique identifier (from Firebase Auth)                 | Yes      | -                 | Primary key                                                                |
| email                  | string    | User's email address                                   | Yes      | -                 | Must be unique (for enterprise account it shoulkd use the company's domain |
| fullName               | string    | User's full name                                       | Yes      | -                 |                                                                            |
| profileImageUrl        | string    | URL to profile image                                   | No       | null              |                                                                            |
| createdAt              | timestamp | Account creation timestamp                             | Yes      | serverTimestamp() |                                                                            |
| updatedAt              | timestamp | Last update timestamp                                  | Yes      | serverTimestamp() |                                                                            |
| accountType            | string    | Type of account (individual, enterprise)               | Yes      | "individual"      |                                                                            |
| userRole               | string    | User role in system (Recruiter, Hiring Manager, Admin) | Yes      | "Recruiter"       | Recruiter (default), Hiring Manager, Admin.                                |
| companyId              | string    | Reference to company                                   | No       | null              | Foreign key to companies collection. Null for candidates.                  |
| settings               | map       | User preferences                                       | No       | {}                |                                                                            |
| settings.notifications | boolean   | Email notification preference                          | No       | true              |                                                                            |
| settings.theme         | string    | UI theme preference                                    | No       | "light"           |                                                                            |
| lastActive             | timestamp | Last user activity                                     | No       | null              |                                                                            |
| department             | string    | User's organizational department                       | No       | null              |                                                                            |
| isVerified             | boolean   | Email verification status                              | Yes      | false             |                                                                            |
| status                 | string    | Account status (active, suspended)                     | Yes      | "active"          |                                                                            |

**Notes:**

- `companyId` enables multi-tenant support. Only Hiring Managers, Recruiters, and Admins have a companyId; Candidates do not.

**Indexes**:

- email (ASC): For email lookup during authentication
- accountType (ASC), createdAt (DESC): For filtering users by account type and signup date
- companyId (ASC): For filtering users by company

**Relationships**:

- One-to-many with `jobs`: A user can create multiple jobs
- One-to-many with `applications`: A user can be associated with multiple applications

**Common Queries**:

- Get user by ID:`users.doc(userId).get()`
- Get user by email:`users.where('email', '==', email).limit(1).get()`
- Get active enterprise users:`users.where('accountType', '==', 'enterprise').where('status', '==', 'active').get()`
- Get all jobs associated to a user:`jobs.where('userId', '==', userId).get()`
- Get all applications associated to a user:`applications.where('userId', '==', userId).get()`
- Get all interviews associated to a user:`interviews.where('userId', '==', userId).get()`
- Get all interview evaluations associated to a user:`interviewEvaluations.where('userId', '==', userId).get()`
- Get total applications for a user:`applications.where('userId', '==', userId).count()`
- Get pass rate for a user:`interviewEvaluations.where('userId', '==', userId).where('result', '==', 'pass').count() / interviewEvaluations.where('userId', '==', userId).count()`
- Get highest scored applications for a user:`interviewEvaluations.where('userId', '==', userId).orderBy('score', 'desc').limit(N).get()`
- Get interviews conducted for a user:`interviews.where('userId', '==', userId).count()`

### jobs

**Purpose**: Store job posting information including details, requirements, and status. (Formerly called "roles"; renamed to avoid confusion with user roles.)

**Fields**:

| Field                                 | Type      | Description                                                                                              | Required | Default           | Notes                            |
| ------------------------------------- | --------- | -------------------------------------------------------------------------------------------------------- | -------- | ----------------- | -------------------------------- |
| id                                    | string    | Unique identifier                                                                                        | Yes      | auto-id           | Primary key                      |
| userId                                | string    | ID of the user who created the job                                                                       | Yes      | -                 | Foreign key to users             |
| companyId                             | string    | Reference to company                                                                                     | Yes      | -                 | Foreign key to companies         |
| title                                 | string    | Job title                                                                                                | Yes      | -                 |                                  |
| summary                               | string    | Brief job description                                                                                    | No       | null              |                                  |
| status                                | string    | Job status (intake, sourcing, screening, deepDive, inPerson, offer, onboarding, hired, rejected, closed) | Yes      | "intake"          | **Standardized camelCase** |
| jobType                               | string    | Employment type (Full-time, Part-time, Contract, Internship)                                             | No       | null              |                                  |
| priority                              | string    | Job priority (Normal, Expedited)                                                                         | No       | "Normal"          |                                  |
| departmentId                          | string    | Department ID                                                                                            | No       | null              |                                  |
| departmentName                        | string    | Department name                                                                                          | No       | null              |                                  |
| hiringManagerId                       | string    | User ID of hiring manager                                                                                | No       | null              | Foreign key to users             |
| hiringManagerContact                  | string    | Contact information for hiring manager                                                                   | No       | null              |                                  |
| location                              | map       | Location details                                                                                         | No       | -                 |                                  |
| location.city                         | string    | City                                                                                                     | No       | ""                |                                  |
| location.remoteStatus                 | string    | Remote work status (Remote, Hybrid, On-site)                                                             | No       | "Remote"          |                                  |
| requiredSkills                        | map       | Map of required skills                                                                                   | No       | {}                |                                  |
| preferredSkills                       | map       | Map of preferred skills                                                                                  | No       | {}                |                                  |
| keyResponsibilities                   | array     | List of key responsibilities                                                                             | No       | []                |                                  |
| experience                            | map       | Experience requirements                                                                                  | No       | {}                |                                  |
| experience.minimum                    | number    | Minimum years of experience                                                                              | No       | 0                 |                                  |
| experience.preferred                  | number    | Preferred years of experience                                                                            | No       | 0                 |                                  |
| education                             | map       | Education requirements                                                                                   | No       | {}                |                                  |
| education.degree                      | string    | Required degree                                                                                          | No       | null              |                                  |
| education.field                       | string    | Field of study                                                                                           | No       | null              |                                  |
| salary                                | map       | Salary information                                                                                       | No       | {}                |                                  |
| salary.min                            | number    | Minimum salary                                                                                           | No       | null              |                                  |
| salary.max                            | number    | Maximum salary                                                                                           | No       | null              |                                  |
| salary.currency                       | string    | Currency code                                                                                            | No       | "USD"             |                                  |
| benefits                              | map       | Benefits information                                                                                     | No       | {}                |                                  |
| benefits.healthInsurance              | boolean   | Offers health insurance                                                                                  | No       | false             |                                  |
| benefits.vacationDays                 | number    | Number of vacation days                                                                                  | No       | 0                 |                                  |
| benefits.retirementPlan               | boolean   | Offers retirement plan                                                                                   | No       | false             |                                  |
| interviewProcess                      | array     | List of interview stages                                                                                 | No       | -                 |                                  |
| interviewProcess[].stage              | string    | Stage name                                                                                               | No       | -                 |                                  |
| interviewProcess[].duration           | string    | Duration (e.g., "30 minutes")                                                                            | No       | ""                |                                  |
| interviewProcess[].description        | string    | Stage description                                                                                        | No       | ""                |                                  |
| interviewProcess[].customInstructions | string    | Custom instructions for stage                                                                            | No       | ""                |                                  |
| team                                  | string    | Team name                                                                                                | No       | ""                |                                  |
| teamDynamic                           | string    | Team dynamic description                                                                                 | No       | ""                |                                  |
| keyStakeholders                       | array     | List of stakeholders                                                                                     | No       | []                |                                  |
| isPublished                           | boolean   | Whether job is published                                                                                 | Yes      | false             |                                  |
| publishedAt                           | timestamp | Timestamp when job was published                                                                         | No       | null              |                                  |
| createdAt                             | timestamp | Creation timestamp                                                                                       | Yes      | serverTimestamp() |                                  |
| updatedAt                             | timestamp | Last update timestamp                                                                                    | Yes      | serverTimestamp() |                                  |

**Indexes**:

**Simple Indexes**:

- userId (ASC): For getting all jobs for a user
- status (ASC): For filtering jobs by status
- isPublished (ASC): For getting published jobs
- jobType (ASC): For filtering by job type
- companyId (ASC): For multi-tenant job queries

**Compound Indexes**:

- userId (ASC), status (ASC), updatedAt (DESC): For user's jobs by status and recency
- userId (ASC), priority (ASC), updatedAt (DESC): For user's jobs by priority
- userId (ASC), departmentId (ASC): For filtering user's jobs by department
- isPublished (ASC), publishedAt (DESC): For published jobs pagination
- isPublished (ASC), jobType (ASC), publishedAt (DESC): For public job type filtering
- isPublished (ASC), location.city (ASC), publishedAt (DESC): For public location filtering
- isPublished (ASC), location.remoteStatus (ASC), publishedAt (DESC): For public remote status filtering
- companyId (ASC), status (ASC), updatedAt (DESC): For multi-tenant job management
- companyId (ASC), isPublished (ASC), updatedAt (DESC): For company published jobs

**Relationships**:

- Many-to-one with `users`: Each job belongs to a user (owner/creator)
- Many-to-one with `companies`: Each job is associated with a company via companyId (future-proof for multi-tenant)
- One-to-many with `templates`: A job can have multiple interview templates
- One-to-one with `jobPostings`: A job can have one job posting
- One-to-many with `applications`: A job can have multiple applications
- One-to-many with `interviews`: A job can have multiple interview sessions

**Common Queries**:

- Get job by ID:`jobs.doc(jobId).get()`
- Get all jobs for a user:`jobs.where('userId', '==', userId).get()`
- Get all published jobs:`jobs.where('isPublished', '==', true).get()`
- Get high-priority jobs for a user:`jobs.where('userId', '==', userId).where('priority', '==', 'High').get()`
- Get recently updated jobs:`jobs.where('userId', '==', userId).orderBy('updatedAt', 'desc').limit(10).get()`
- Get jobs in specific stages:`jobs.where('userId', '==', userId).where('status', 'in', ['screening', 'deepDive']).get()`

### templates

**Purpose**: Store interview templates with configuration for interviews.

**Fields**:

| Field                            | Type      | Description                                              | Required | Default           | Notes                                        |
| -------------------------------- | --------- | -------------------------------------------------------- | -------- | ----------------- | -------------------------------------------- |
| id                               | string    | Unique identifier                                        | Yes      | auto-id           | Primary key                                  |
| jobId                            | string    | Associated job ID                                        | Yes      | -                 | Foreign key to jobs                          |
| createdBy                        | string    | User ID of the creator                                   | Yes      | -                 | **Standardized field naming**          |
| companyId                        | string    | ID of company associated with the template               | Yes      | -                 | Foreign key to companies                     |
| stageName                        | string    | Interview stage name                                     | Yes      | "Screening"       |                                              |
| stageIndex                       | number    | Order of interview (0, 1, 2...) as for first, second,... | Yes      | 0                 |                                              |
| customInstructions               | string    | Custom instructions for the interview                    | No       | ""                | Matches current Firestore structure          |
| duration                         | string    | Interview duration (e.g., "1 hour")                      | Yes      | "30 minutes"      | Changed to string to match current structure |
| status                           | string    | Template status (Active, Archived, Draft)                | Yes      | "Active"          | Matches current Firestore structure          |
| passRate                         | number    | Required score to pass (0-100)                           | Yes      | 0.8               | Matches current Firestore structure          |
| statistics                       | map       | Usage statistics                                         | Yes      | -                 | Matches current Firestore structure          |
| statistics.averageScore          | number    | Average score across all interviews                      | Yes      | 0                 |                                              |
| statistics.topScore              | number    | Highest score achieved                                   | Yes      | 0                 |                                              |
| statistics.topPerformerIds       | array     | IDs of top performing candidates                         | Yes      | []                |                                              |
| statistics.candidatesInterviewed | number    | Number of candidates interviewed                         | Yes      | 0                 |                                              |
| statistics.passRate              | number    | Actual pass rate                                         | Yes      | 0                 |                                              |
| createdAt                        | timestamp | Creation timestamp                                       | Yes      | serverTimestamp() |                                              |
| updatedAt                        | timestamp | Last update timestamp                                    | Yes      | serverTimestamp() |                                              |

**Indexes**:

- jobId (ASC), stageIndex (ASC): For finding templates for a job in sequence
- createdBy (ASC), stageName (ASC): For finding user's templates by stage
- jobId (ASC), status (ASC): For finding active templates for a job
- companyId (ASC), updatedAt (DESC): For finding recent templates for a company

**Relationships**:

- Many-to-one with `jobs`: Each template belongs to a job
- Many-to-one with `users`: Each template is created by a user
- One-to-many with `questions`: A template has multiple questions
- One-to-many with `criteria`: A template has multiple evaluation criteria
- One-to-many with `interviews`: Many interview sessions could be associated to an interview template

**Common Queries**:

- Get template by ID: `templates.doc(templateId).get()`
- Get all templates for a job: `templates.where('jobId', '==', jobId).get()`
- Get templates for a job by stage: `templates.where('jobId', '==', jobId).where('stageName', '==', 'Cultural & Company fit').get()`
- Get templates in order: `templates.where('jobId', '==', jobId).orderBy('stageIndex', 'asc').get()`
- Get active templates for a job: `templates.where('jobId', '==', jobId).where('status', '==', 'Active').get()`
- Get templates created by a user: `templates.where('createdBy', '==', userId).get()`

### questions

**Purpose**: Store interview questions associated with templates.

**Fields**:

| Field                            | Type      | Description                                  | Required | Default           | Notes                               |
| -------------------------------- | --------- | -------------------------------------------- | -------- | ----------------- | ----------------------------------- |
| id                               | string    | Unique identifier                            | Yes      | auto-id           | Primary key                         |
| templateId                       | string    | ID of the template this question belongs to  | Yes      | -                 | Foreign key to templates            |
| jobId                            | string    | ID of the associated job                     | Yes      | -                 | Foreign key to jobs                 |
| createdBy                        | string    | ID of the user who created the question      | Yes      | -                 | **Standardized field naming** |
| companyId                        | string    | ID of company associated with the question   | Yes      | -                 | Foreign key to companies            |
| stageName                        | string    | Interview stage this question belongs to     | Yes      | -                 | Matches template structure          |
| stageIndex                       | number    | Order of interview stage                     | Yes      | 0                 | Matches template structure          |
| question                         | string    | The question text                            | Yes      | -                 |                                     |
| text                             | string    | Duplicate of question text for compatibility | Yes      | -                 | For UI compatibility                |
| purpose                          | string    | Purpose of asking this question              | No       | ""                | Matches current structure           |
| idealAnswerCriteria              | string    | Criteria for an ideal answer                 | No       | ""                | Matches current structure           |
| statistics                       | map       | Usage statistics                             | Yes      | -                 | Matches current structure           |
| statistics.averageScore          | number    | Average score for this question              | Yes      | 0                 |                                     |
| statistics.topScore              | number    | Highest score for this question              | Yes      | 0                 |                                     |
| statistics.totalAnswers          | number    | Number of times this question was answered   | Yes      | 0                 |                                     |
| statistics.topScoringCandidateId | string    | ID of candidate with highest score           | Yes      | ""                |                                     |
| sequence                         | number    | Order in the interview                       | Yes      | 0                 |                                     |
| createdAt                        | timestamp | Creation timestamp                           | Yes      | serverTimestamp() |                                     |
| updatedAt                        | timestamp | Last update timestamp                        | Yes      | serverTimestamp() |                                     |

**Indexes**:

- templateId (ASC), sequence (ASC): For getting questions in sequence order
- jobId (ASC), stageName (ASC): For getting questions by interview stage
- templateId (ASC), statistics.averageScore (DESC): For finding best performing questions
- companyId (ASC), updatedAt (DESC): For finding recently updated questions
- createdBy (ASC), jobId (ASC): For finding questions created by a user for a job

**Relationships**:

- Many-to-one with `templates`: Each question belongs to a template
- Many-to-one with `jobs`: Each question is associated with a job

**Common Queries**:

- Get question by ID: `questions.doc(questionId).get()`
- Get all questions for a template: `questions.where('templateId', '==', templateId).orderBy('sequence', 'asc').get()`
- Get questions for a specific stage: `questions.where('jobId', '==', jobId).where('stageName', '==', 'Cultural & Company fit').get()`
- Get questions by stage order: `questions.where('templateId', '==', templateId).orderBy('sequence', 'asc').get()`
- Get top performing questions: `questions.where('templateId', '==', templateId).orderBy('statistics.averageScore', 'desc').limit(5).get()`
- Get questions created by a user: `questions.where('createdBy', '==', userId).get()`

### criteria

**Purpose**: Store evaluation criteria for assessing candidates.

**Fields**:

| Field       | Type      | Description                                                 | Required | Default           | Notes                                        |
| ----------- | --------- | ----------------------------------------------------------- | -------- | ----------------- | -------------------------------------------- |
| id          | string    | Unique identifier                                           | Yes      | auto-id           | Primary key                                  |
| templateId  | string    | ID of the template this criterion belongs to                | Yes      | -                 | Foreign key to templates                     |
| jobId       | string    | ID of the associated job                                    | Yes      | -                 | Foreign key to jobs                          |
| createdBy   | string    | ID of the user who created the criterion                    | Yes      | -                 | **Standardized field naming**          |
| companyId   | string    | ID of company associated with the criterion                 | Yes      | -                 | Foreign key to companies                     |
| stageName   | string    | Interview stage this criterion belongs to                   | Yes      | -                 | Matches template structure                   |
| stageIndex  | number    | Order of interview stage                                    | Yes      | 0                 | Matches template structure                   |
| type        | string    | Type of criteria (ScoreCard, BetweenTheLines, Disqualifier) | Yes      | "ScoreCard"       | Matches current structure                    |
| competency  | string    | Competency being evaluated                                  | No       | ""                | For ScoreCard type                           |
| description | string    | The specific criteria being evaluated                       | Yes      | -                 | Renamed from 'criteria' to avoid confusion   |
| weight      | number    | Weight in overall evaluation (0.0-1.0)                      | No       | 0.0               | For ScoreCard type, nullable for other types |
| sequence    | number    | Order in evaluation form                                    | Yes      | 0                 |                                              |
| createdAt   | timestamp | Creation timestamp                                          | Yes      | serverTimestamp() |                                              |
| updatedAt   | timestamp | Last update timestamp                                       | Yes      | serverTimestamp() |                                              |

**Indexes**:

- templateId (ASC), sequence (ASC): For getting criteria in sequence order
- templateId (ASC), type (ASC): For filtering criteria by type
- jobId (ASC), stageName (ASC): For getting criteria by interview stage
- templateId (ASC), weight (DESC): For getting criteria by importance (for ScoreCard type)
- companyId (ASC), updatedAt (DESC): For finding recently updated criteria
- createdBy (ASC), jobId (ASC): For finding criteria created by a user for a job

**Relationships**:

- Many-to-one with `templates`: Each criterion belongs to a template
- Many-to-one with `jobs`: Each criterion is associated with a job
- Referenced in `interviewEvaluations`: Criteria are referenced in evaluation scores

**Common Queries**:

- Get criterion by ID: `criteria.doc(criterionId).get()`
- Get all criteria for a template: `criteria.where('templateId', '==', templateId).orderBy('sequence', 'asc').get()`
- Get criteria by type: `criteria.where('templateId', '==', templateId).where('type', '==', 'ScoreCard').get()`
- Get disqualifiers for a template: `criteria.where('templateId', '==', templateId).where('type', '==', 'Disqualifier').get()`
- Get between-the-lines criteria: `criteria.where('templateId', '==', templateId).where('type', '==', 'BetweenTheLines').get()`
- Get weighted criteria in order: `criteria.where('templateId', '==', templateId).where('type', '==', 'ScoreCard').orderBy('weight', 'desc').get()`
- Get criteria for a specific stage: `criteria.where('jobId', '==', jobId).where('stageName', '==', 'Cultural & Company fit').get()`

### applications

**Purpose**: Store candidate applications with denormalized display fields for efficient list views.

**Fields**:

| Field                             | Type             | Description                                                                                 | Required      | Default           | Notes                                         |
| --------------------------------- | ---------------- | ------------------------------------------------------------------------------------------- | ------------- | ----------------- | --------------------------------------------- |
| id                                | string           | Unique identifier                                                                           | Yes           | auto-id           | Primary key                                   |
| jobId                             | string           | Associated job ID                                                                           | Yes           | -                 | Foreign key to jobs                           |
| candidateId                       | string           | ID of the candidate                                                                         | Yes           | -                 | Foreign key to candidates                     |
| userId                            | string           | ID of the job owner                                                                         | Yes           | -                 | Foreign key to users                          |
| companyId                         | string           | Reference to company                                                                        | Yes           | -                 | Foreign key to companies (inherited from job) |
| **candidateName**           | **string** | **Candidate full name (denormalized)**                                                | **Yes** | **-**       | **Eliminates candidate lookup**         |
| **candidateEmail**          | **string** | **Candidate email (denormalized)**                                                    | **Yes** | **-**       | **For display and search**              |
| **jobTitle**                | **string** | **Job title (denormalized)**                                                          | **Yes** | **-**       | **Eliminates job lookup**               |
| **jobTeam**                 | **string** | **Job team name (denormalized)**                                                      | **No**  | **""**      | **For filtering**                       |
| **jobDepartment**           | **string** | **Job department (denormalized)**                                                     | **No**  | **null**    | **For filtering**                       |
| **createdByName**           | **string** | **Job creator name (denormalized)**                                                   | **Yes** | **-**       | **Eliminates user lookup**              |
| status                            | string           | Application status (submitted, screening, interview, evaluation, offer, accepted, rejected) | Yes           | "submitted"       | **Standardized camelCase**              |
| source                            | string           | Application source (Website, Referral, LinkedIn, etc.)                                      | No            | "Website"         |                                               |
| resumeId                          | string           | Resume document ID (references resumes collection)                                          | Yes           | -                 | Each application links to exactly one resume  |
| **resumeScore**             | **number** | **Resume evaluation score (denormalized)**                                            | **No**  | **null**    | **Quick display without lookup**        |
| **resumeDecision**          | **string** | **Resume pass/fail decision (denormalized)**                                          | **No**  | **null**    | **Quick filtering**                     |
| **latestInterviewScore**    | **number** | **Latest interview score (denormalized)**                                             | **No**  | **null**    | **Quick display without lookup**        |
| **latestInterviewDecision** | **string** | **Latest interview decision (denormalized)**                                          | **No**  | **null**    | **Quick filtering**                     |
| parsedResumeText                  | string           | Parsed text extracted from resume                                                           | No            | null              | For downstream analysis                       |
| submittedAt                       | timestamp        | Submission timestamp                                                                        | Yes           | serverTimestamp() |                                               |
| updatedAt                         | timestamp        | Last update timestamp                                                                       | Yes           | serverTimestamp() |                                               |
| currentStage                      | map              | Current interview stage information                                                         | No            | null              |                                               |
| currentStage.name                 | string           | Stage name                                                                                  | No            | null              |                                               |
| currentStage.startedAt            | timestamp        | Stage start timestamp                                                                       | No            | null              |                                               |
| notes                             | string           | Internal notes                                                                              | No            | ""                |                                               |
| tags                              | array            | Tags for categorization                                                                     | No            | []                |                                               |
| customFields                      | map              | Additional application fields                                                               | No            | {}                |                                               |
| referralInfo                      | map              | Referral information                                                                        | No            | null              |                                               |
| referralInfo.name                 | string           | Referrer name                                                                               | No            | null              |                                               |
| referralInfo.relationship         | string           | Relationship to candidate                                                                   | No            | null              |                                               |
| resumeEvaluationScore             | number           | Initial resume evaluation score                                                            | No            | null              |                                               |
| overallScore                      | number           | Overall interview evaluation score for the latest interview completed                      | No            | null              |                                               |
| relevantExperience                | number           | Years of relevant experience                                                                | No            | null              |                                               |
| hasPendingAction                  | boolean          | Whether action is needed                                                                    | Yes           | false             |                                               |
| lastInteractionAt                 | timestamp        | Last interaction timestamp                                                                  | No            | null              |                                               |
| rejectionReason                   | string           | Reason if rejected                                                                          | No            | null              |                                               |
| nextStep                          | string           | next interview stage by index or humanReview as the last step                               | No            | null              |                                               |

**Performance-Critical Indexes**:

**Multi-tenant Pagination (ESSENTIAL)**:

- companyId (ASC), submittedAt (DESC): Primary pagination for company applications
- companyId (ASC), status (ASC), submittedAt (DESC): Status-filtered pagination
- companyId (ASC), jobId (ASC), submittedAt (DESC): Job-specific pagination

**Filtering & Sorting (HIGH PRIORITY)**:

- companyId (ASC), resumeDecision (ASC), submittedAt (DESC): Resume decision filtering
- companyId (ASC), latestInterviewDecision (ASC), submittedAt (DESC): Interview decision filtering
- companyId (ASC), jobTeam (ASC), submittedAt (DESC): Team-based filtering
- companyId (ASC), createdByName (ASC), submittedAt (DESC): Creator filtering

**Score-based Queries**:

- companyId (ASC), resumeScore (DESC): Resume score ranking
- companyId (ASC), latestInterviewScore (DESC): Interview score ranking
- companyId (ASC), status (ASC), latestInterviewScore (DESC): Status + score ranking

**Search & Lookup**:

- candidateId (ASC), submittedAt (DESC): Candidate application history
- candidateEmail (ASC): Email-based lookup
- companyId (ASC), candidateEmail (ASC): Email-based lookups within company
- jobId (ASC), status (ASC), submittedAt (DESC): Job-specific status filtering

**Relationships**:

- Many-to-one with `jobs`: Each application is for a specific job
- Many-to-one with `candidates`: Each application belongs to a candidate
- Many-to-one with `users`: Each application is associated with a job owner
- One-to-many with `interviewEvaluations`: An application can have multiple interview evaluations
- One-to-many with `interviews`: An application can have multiple interviews

**Optimized Query Patterns**:

**Paginated List Views (PRIMARY USE CASE)**:

```javascript
// Page 1: Get first 20 applications for company
applications.where('companyId', '==', companyId)
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .get()

// Page 2: Get next 20 applications using cursor
applications.where('companyId', '==', companyId)
  .orderBy('submittedAt', 'desc')
  .startAfter(lastDocumentFromPreviousPage)
  .limit(20)
  .get()

// Filtered pagination: Applications by status
applications.where('companyId', '==', companyId)
  .where('status', '==', 'interview')
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .get()

// Filtered pagination: Applications by resume decision
applications.where('companyId', '==', companyId)
  .where('resumeDecision', '==', 'Pass')
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .get()
```

**Single Document Lookups**:

- Get application by ID: `applications.doc(applicationId).get()`
- Get candidate applications: `applications.where('candidateId', '==', candidateId).orderBy('submittedAt', 'desc').get()`

**Aggregate Queries (for dashboard)**:

- Count by status: `applications.where('companyId', '==', companyId).where('status', '==', 'Interview').count().get()`
- Count by resume decision: `applications.where('companyId', '==', companyId).where('resumeDecision', '==', 'Pass').count().get()`

### candidates

**Purpose**: Store candidate information independent of specific applications and companies. Candidates are users, but not directly associated with any company. (Future: Candidates will authenticate and have their own portal.)

**Fields**:

| Field                      | Type      | Description                | Required | Default           | Notes          |
| -------------------------- | --------- | -------------------------- | -------- | ----------------- | -------------- |
| id                         | string    | Unique identifier          | Yes      | auto-id           | Primary key    |
| email                      | string    | Candidate's email address  | Yes      | -                 | Must be unique |
| fullName                   | string    | Candidate's full name      | Yes      | -                 |                |
| phone                      | string    | Phone number               | No       | null              |                |
| professionalSummary        | string    | Brief professional summary | No       | ""                |                |
| currentPosition            | string    | Current job title          | No       | null              |                |
| currentCompany             | string    | Current employer           | No       | null              |                |
| location                   | map       | Location information       | No       | -                 |                |
| location.city              | string    | City                       | No       | null              |                |
| location.state             | string    | State/province             | No       | null              |                |
| location.country           | string    | Country                    | No       | null              |                |
| skills                     | array     | List of skills             | No       | []                |                |
| yearsOfExperience          | number    | Total years of experience  | No       | null              |                |
| education                  | array     | Education history          | No       | []                |                |
| education[].institution    | string    | Institution name           | No       | null              |                |
| education[].degree         | string    | Degree earned              | No       | null              |                |
| education[].field          | string    | Field of study             | No       | null              |                |
| education[].year           | number    | Graduation year            | No       | null              |                |
| workHistory                | array     | Work experience            | No       | []                |                |
| workHistory[].company      | string    | Company name               | No       | null              |                |
| workHistory[].position     | string    | Job title                  | No       | null              |                |
| workHistory[].startDate    | string    | Start date (YYYY-MM)       | No       | null              |                |
| workHistory[].endDate      | string    | End date (YYYY-MM)         | No       | null              |                |
| workHistory[].description  | string    | Job description            | No       | null              |                |
| linkedInUrl                | string    | LinkedIn profile URL       | No       | null              |                |
| githubUrl                  | string    | GitHub profile URL         | No       | null              |                |
| portfolioUrl               | string    | Portfolio URL              | No       | null              |                |
| availabilityDate           | string    | Earliest start date        | No       | null              |                |
| salaryExpectation          | map       | Salary expectations        | No       | null              |                |
| salaryExpectation.amount   | number    | Expected amount            | No       | null              |                |
| salaryExpectation.currency | string    | Currency code              | No       | "USD"             |                |
| salaryExpectation.type     | string    | Hourly/annual/etc          | No       | "annual"          |                |
| createdAt                  | timestamp | Creation timestamp         | Yes      | serverTimestamp() |                |
| updatedAt                  | timestamp | Last update timestamp      | Yes      | serverTimestamp() |                |
| source                     | string    | How candidate was found    | No       | null              |                |
| tags                       | array     | Tags for categorization    | No       | []                |                |
| isRedacted                 | boolean   | Whether PII is redacted    | Yes      | false             |                |
| lastActivityAt             | timestamp | Last activity timestamp    | No       | null              |                |
| averageScore               | number    | Average evaluation score   | No       | null              |                |

**Indexes**:

**Simple Indexes**:

- email (ASC): For finding candidates by email
- updatedAt (DESC): For getting recently updated candidates
- averageScore (DESC): For ranking candidates by score
- createdAt (DESC): For getting newest candidates
- currentCompany (ASC): For filtering candidates by current employer

**Compound Indexes**:

- yearsOfExperience (ASC), createdAt (DESC): For experience-based pagination
- skills (ARRAY_CONTAINS_ANY), createdAt (DESC): For skill-based search with pagination
- averageScore (DESC), createdAt (DESC): For score-based ranking with pagination

**Relationships**:

- One-to-many with `resumes`: A candidate can have multiple resumes (see resumes collection)
- One-to-many with `applications`: A candidate can have multiple applications
- One-to-many with `interviewEvaluations`: A candidate can have multiple interview evaluations
- One-to-many with `resumeEvaluations`: A candidate can have multiple resume evaluations (via applications)
- One-to-many with `interviews`: A candidate can participate in multiple interviews

**Common Queries**:

- Get candidate by ID:`candidates.doc(candidateId).get()`
- Get candidate by email:`candidates.where('email', '==', email).limit(1).get()`
- Get all resumes for candidate:`resumes.where('candidateId', '==', candidateId).get()`
- Get all applications for candidate:`applications.where('candidateId', '==', candidateId).get()`
- Get all resume evaluations for candidate:`resumeEvaluations.where('candidateId', '==', candidateId).get()`
- Get all interview evaluations for candidate:`interviewEvaluations.where('candidateId', '==', candidateId).get()`
- Get candidates with specific skills:`candidates.where('skills', 'array-contains-any', ['React', 'Node.js']).get()`
- Get top-rated candidates:`candidates.orderBy('averageScore', 'desc').limit(20).get()`
- Get recently added candidates:`candidates.orderBy('createdAt', 'desc').limit(50).get()`
- Get candidates by years of experience:`candidates.where('yearsOfExperience', '>=', 5).get()`

### resumes

**Purpose**: Store metadata for uploaded resume files. Each resume is associated with a candidate. Candidates can have multiple resumes. Each application references one resume.

**Fields**:

| Field       | Type      | Description                                    | Required | Default           | Notes                                           |
| ----------- | --------- | ---------------------------------------------- | -------- | ----------------- | ----------------------------------------------- |
| id          | string    | Unique identifier                              | Yes      | auto-id           | Primary key                                     |
| candidateId | string    | Candidate ID (owner of resume)                 | Yes      | -                 | Foreign key to candidates                       |
| fileUrl     | string    | Firebase Storage URL to uploaded file          | Yes      | -                 |                                                 |
| fileName    | string    | Original file name                             | Yes      | -                 |                                                 |
| uploadedAt  | timestamp | Upload timestamp                               | Yes      | serverTimestamp() |                                                 |
| fileType    | string    | MIME type (e.g., application/pdf)              | Yes      | -                 |                                                 |
| fileSize    | number    | File size in bytes                             | Yes      | -                 |                                                 |
| isPrimary   | boolean   | Marked as primary resume for candidate         | No       | false             | Only one resume per candidate should be primary |
| parsedText  | string    | Parsed text from resume (optional, for search) | No       | null              | For search, not for analysis (see application)  |
| metadata    | map       | Additional metadata (version, source, etc)     | No       | {}                |                                                 |
| tags        | array     | Tags for categorization                        | No       | []                |                                                 |
| updatedAt   | timestamp | Last update timestamp                          | Yes      | serverTimestamp() |                                                 |

**Indexes**:

- candidateId (ASC), uploadedAt (DESC): For getting all resumes for a candidate
- candidateId (ASC), isPrimary (ASC): For finding primary resume efficiently
- isPrimary (ASC): For finding the primary resume
- fileType (ASC): For filtering by file type

**Relationships**:

- Many-to-one with `candidates`: Each resume belongs to a candidate
- One-to-many with `applications`: Each resume can be referenced by multiple applications (but each application references only one resume)

**Common Queries**:

- Get all resumes for a candidate:`resumes.where('candidateId', '==', candidateId).get()`
- Get resume by ID:`resumes.doc(resumeId).get()`
- Get primary resume for a candidate:`resumes.where('candidateId', '==', candidateId).where('isPrimary', '==', true).limit(1).get()`

### resumeEvaluations

**Purpose**: Store evaluation results for resumes. Each resume evaluation is linked to an application and a resume. Resume evaluations are distinct from interview evaluations.

**Fields**:

| Field             | Type      | Description                                    | Required | Default           | Notes                        |
| ----------------- | --------- | ---------------------------------------------- | -------- | ----------------- | ---------------------------- |
| id                | string    | Unique identifier                              | Yes      | auto-id           | Primary key                  |
| applicationId     | string    | Associated application ID                      | Yes      | -                 | Foreign key to applications  |
| resumeId          | string    | Resume document ID (references resumes)        | Yes      | -                 | Foreign key to resumes       |
| candidateId       | string    | Candidate ID                                   | Yes      | -                 | Foreign key to candidates    |
| evaluatorId       | string    | ID of evaluator (user)                         | Yes      | -                 | Foreign key to users         |
| scores            | map       | Map of evaluation criteria and scores          | Yes      | -                 | E.g., {criteria: score, ...} |
| overallScore      | number    | Overall resume score (0-100)                   | Yes      | -                 |                              |
| evaluationSummary | string    | Evaluation summary                             | No       | ""                |                              |
| strengths         | array     | List of strengths                              | No       | []                |                              |
| weaknesses        | array     | List of weaknesses                             | No       | []                |                              |
| recommendation    | string    | Recommendation (Maybe, Reject, Pass)           | Yes      | -                 |                              |
| status            | string    | Evaluation status (Draft, Completed, Reviewed) | Yes      | "Draft"           |                              |
| createdAt         | timestamp | Creation timestamp                             | Yes      | serverTimestamp() |                              |
| updatedAt         | timestamp | Last update timestamp                          | Yes      | serverTimestamp() |                              |
| notes             | string    | Internal notes                                 | No       | ""                |                              |

**Indexes**:

- applicationId (ASC): For getting resume evaluation for an application
- resumeId (ASC): For getting all evaluations for a resume
- candidateId (ASC): For getting all resume evaluations for a candidate
- evaluatorId (ASC), createdAt (DESC): For getting evaluations by evaluator

**Relationships**:

- One-to-one with `applications`: Each application may have one resume evaluation
- Many-to-one with `resumes`: Each resume evaluation is for a specific resume
- Many-to-one with `candidates`: Each resume evaluation is for a candidate
- Many-to-one with `users`: Each resume evaluation is created by a user (evaluator)

**Common Queries**:

- Get resume evaluation for an application:`resumeEvaluations.where('applicationId', '==', applicationId).limit(1).get()`
- Get all resume evaluations for a candidate:`resumeEvaluations.where('candidateId', '==', candidateId).get()`
- Get all resume evaluations for a resume:`resumeEvaluations.where('resumeId', '==', resumeId).get()`
- Get resume evaluations by evaluator:`resumeEvaluations.where('evaluatorId', '==', userId).get()`

### interviewEvaluations

**Purpose**: Store interview evaluation results for candidates (distinct from resume evaluations).

**Fields**:

| Field                                  | Type      | Description                                         | Required | Default | Notes                                         |
| -------------------------------------- | --------- | --------------------------------------------------- | -------- | ------- | --------------------------------------------- |
| id                                     | string    | Unique identifier                                   | Yes      | auto-id | Primary key                                   |
| metadata                               | map       | Metadata about the evaluation                       | Yes      | -       |                                               |
| metadata.applicationId                 | string    | Associated application ID                           | Yes      | -       | Foreign key to applications                   |
| metadata.jobId                         | string    | Associated role/job ID                              | Yes      | -       | Foreign key to jobs                           |
| metadata.interviewId                   | string    | ID of associated interview session                  | Yes      | -       | Foreign key to interviews                     |
| companyId                              | string    | Reference to company                                | Yes      | -       | Foreign key to companies (inherited from job) |
| metadata.evaluatedAt                   | timestamp | Timestamp when evaluation was completed             | Yes      | -       | Firebase Timestamp for proper sorting         |
| metadata.evaluatedBy                   | string    | Who performed the evaluation (e.g., "AI")           | Yes      | -       |                                               |
| evaluationSummary                      | map       | Summary of the evaluation results                   | Yes      | -       |                                               |
| evaluationSummary.candidateName        | string    | Name of the candidate                               | Yes      | -       |                                               |
| evaluationSummary.job                  | string    | Job title                                           | Yes      | -       |                                               |
| evaluationSummary.overallScore         | number    | Overall numerical score (0-100)                     | Yes      | -       |                                               |
| evaluationSummary.decision             | string    | Hire decision (Go, No Go, Maybe)                    | Yes      | -       |                                               |
| evaluationSummary.confidence           | string    | Confidence level in the evaluation                  | Yes      | -       | Low, Medium, High                             |
| evaluationSummary.minimumPassRate      | number    | Minimum score required to pass                      | Yes      | -       |                                               |
| evaluationSummary.summary              | string    | Concise evaluation summary                          | Yes      | -       |                                               |
| betweenTheLines                        | array     | Observations about candidate behavior and qualities | No       | []      |                                               |
| betweenTheLines[].criteria             | string    | Criteria being observed                             | Yes      | -       |                                               |
| betweenTheLines[].observation          | string    | What was observed                                   | Yes      | -       |                                               |
| betweenTheLines[].impact               | string    | Impact of the observation on evaluation             | Yes      | -       |                                               |
| decisionReasoning                      | map       | Reasoning behind the decision                       | Yes      | -       |                                               |
| decisionReasoning.strengths            | array     | List of candidate strengths                         | Yes      | []      |                                               |
| decisionReasoning.concerns             | array     | List of concerns about the candidate                | Yes      | []      |                                               |
| decisionReasoning.keyFactors           | array     | Key factors influencing the decision                | Yes      | []      |                                               |
| decisionReasoning.finalRecommendation  | string    | Detailed recommendation                             | Yes      | -       |                                               |
| disqualifierCheck                      | array     | Assessment of potential disqualifiers               | Yes      | []      |                                               |
| disqualifierCheck[].criteria           | string    | Disqualifier criteria                               | Yes      | -       |                                               |
| disqualifierCheck[].evidence           | string    | Evidence related to the criteria                    | Yes      | -       |                                               |
| disqualifierCheck[].explanation        | string    | Explanation of the assessment                       | Yes      | -       |                                               |
| disqualifierCheck[].triggered          | boolean   | Whether the disqualifier was triggered              | Yes      | false   |                                               |
| questionAnalysis                       | array     | Analysis of individual interview questions          | Yes      | []      |                                               |
| questionAnalysis[].question            | string    | The interview question                              | Yes      | -       |                                               |
| questionAnalysis[].answer              | string    | Candidate's answer                                  | Yes      | -       |                                               |
| questionAnalysis[].evaluation          | string    | Evaluation of the answer                            | Yes      | -       |                                               |
| questionAnalysis[].relatedCompetencies | array     | Competencies related to this question               | Yes      | []      |                                               |
| questionAnalysis[].strengths           | array     | Strengths demonstrated in the answer                | Yes      | []      |                                               |
| questionAnalysis[].weaknesses          | array     | Weaknesses demonstrated in the answer               | Yes      | []      |                                               |
| scorecardEvaluation                    | array     | Evaluation of competency areas                      | Yes      | []      |                                               |
| scorecardEvaluation[].competency       | string    | Competency being evaluated                          | Yes      | -       |                                               |
| scorecardEvaluation[].reasoning        | string    | Reasoning for the score                             | Yes      | -       |                                               |
| scorecardEvaluation[].score            | number    | Score for this competency (1-10)                    | Yes      | -       |                                               |
| scorecardEvaluation[].weight           | number    | Weight of this competency                           | Yes      | -       | Decimal between 0 and 1                       |
| scorecardEvaluation[].weightedScore    | number    | Weighted score contribution                         | Yes      | -       |                                               |

**Indexes**:

- metadata.applicationId (ASC), metadata.evaluatedAt (DESC): For getting evaluations for an application
- metadata.jobId (ASC), evaluationSummary.overallScore (DESC): For ranking evaluations by score for a job
- metadata.interviewId (ASC): For finding an evaluation linked to an interview
- evaluationSummary.decision (ASC), evaluationSummary.overallScore (DESC): For filtering evaluations by decision
- metadata.evaluatedBy (ASC), metadata.evaluatedAt (DESC): For filtering evaluations by evaluator (e.g., AI)
- evaluationSummary.minimumPassRate (ASC), evaluationSummary.overallScore (DESC): For analyzing pass rates

**Relationships**:

- Many-to-one with `applications`: Each evaluation is for a specific application
- Many-to-one with `jobs`: Each evaluation is associated with a job/role
- Many-to-one with `candidates`: Each evaluation is for a candidate
- Many-to-one with `interviews`: Each evaluation is linked to an interview session

**Common Queries**:

- Get evaluation by ID: `interviewEvaluations.doc(evaluationId).get()`
- Get evaluations for an application: `interviewEvaluations.where('metadata.applicationId', '==', applicationId).orderBy('metadata.evaluatedAt', 'desc').get()`
- Get top-scoring evaluations: `interviewEvaluations.where('metadata.jobId', '==', jobId).orderBy('evaluationSummary.overallScore', 'desc').limit(10).get()`
- Get evaluations by decision: `interviewEvaluations.where('evaluationSummary.decision', '==', 'Go').get()`
- Get evaluations for an interview: `interviewEvaluations.where('metadata.interviewId', '==', interviewId).limit(1).get()`
- Get AI-generated evaluations: `interviewEvaluations.where('metadata.evaluatedBy', '==', 'AI').get()`
- Get evaluations with triggered disqualifiers: `interviewEvaluations.where('disqualifierCheck', 'array-contains', {'triggered': true}).get()`

### interviews

**Purpose**: Store interview sessions and their metadata.

**Fields**:

| Field                | Type      | Description                                                    | Required | Default           | Notes                                                   |
| -------------------- | --------- | -------------------------------------------------------------- | -------- | ----------------- | ------------------------------------------------------- |
| id                   | string    | Unique identifier                                              | Yes      | auto-id           | Primary key                                             |
| applicationId        | string    | Associated application ID                                      | No       | null              | Foreign key to applications, null for intake interviews |
| jobId                | string    | Associated job ID                                              | Yes      | -                 | Foreign key to jobs                                     |
| templateId           | string    | Template used for interview                                    | No       | null              | Foreign key to templates                                |
| candidateId          | string    | Candidate ID                                                   | No       | null              | Foreign key to candidates                               |
| userId               | string    | Interviewer user ID                                            | Yes      | -                 | Foreign key to users                                    |
| companyId            | string    | Reference to company                                           | Yes      | -                 | Foreign key to companies (inherited from job)           |
| sessionId            | string    | ID of the interview session (deprecated)                       | No       | null              | **DEPRECATED**: No longer used                    |
| stageName            | string    | Name of the interview stage                                    | Yes      | "Screening"       | Matches template.stageName                              |
| stageIndex           | number    | Order of interview stage (0, 1, 2...)                          | Yes      | 0                 | Matches template.stageIndex                             |
| startTime            | timestamp | Start time                                                     | No       | null              |                                                         |
| endTime              | timestamp | End time                                                       | No       | null              |                                                         |
| duration             | number    | Duration in minutes                                            | No       | null              |                                                         |
| status               | string    | Interview status (initiated, inProgress, completed, cancelled) | Yes      | "initiated"       | **Standardized camelCase**                        |
| transcriptId         | string    | ID of associated transcript                                    | No       | null              | Foreign key to transcripts                              |
| evaluationId         | string    | ID of associated evaluation                                    | No       | null              | Foreign key to interviewEvaluations                     |
| hasEvaluation        | boolean   | Whether an evaluation exists                                   | Yes      | false             |                                                         |
| notes                | string    | Interview notes                                                | No       | ""                |                                                         |
| questions            | array     | Questions asked during the interview                           | No       | []                | References questions from template                      |
| questions[].id       | string    | Question ID                                                    | Yes      | -                 | Foreign key to questions collection                     |
| questions[].question | string    | Question text                                                  | Yes      | -                 | Matches questions.question                              |
| questions[].purpose  | string    | Purpose of the question                                        | No       | null              | Matches questions.purpose                               |
| isAutomated          | boolean   | Whether conducted by AI                                        | Yes      | false             |                                                         |
| createdAt            | timestamp | Creation timestamp                                             | Yes      | serverTimestamp() |                                                         |
| updatedAt            | timestamp | Last update timestamp                                          | Yes      | serverTimestamp() |                                                         |
| isPublic             | boolean   | Whether this is public                                         | Yes      | false             |                                                         |

**Indexes**:

- jobId (ASC), status (ASC): For filtering interviews by status for a job
- applicationId (ASC), stageIndex (ASC): For getting interviews for an application in order
- candidateId (ASC), createdAt (DESC): For getting all interviews for a candidate
- userId (ASC), startTime (ASC): For getting upcoming interviews for a user
- status (ASC), startTime (ASC): For finding upcoming interviews
- hasEvaluation (ASC), status (ASC): For finding completed interviews without evaluations
- isAutomated (ASC), status (ASC): For filtering AI-conducted interviews
- transcriptId (ASC): For finding an interview from a transcript
- sessionId (ASC): For finding an interview from a public session
- evaluationId (ASC): For finding an interview from an evaluation
- stageName (ASC), jobId (ASC): For filtering interviews by stage for a job

**Relationships**:

- Many-to-one with `applications`: Each interview is for a specific application
- Many-to-one with `jobs`: Each interview is associated with a job/role
- Many-to-one with `candidates`: Each interview is with a candidate
- Many-to-one with `users`: Each interview is conducted by a user
- Many-to-one with `templates`: Each interview uses a template
- One-to-one with `transcripts`: Each interview has one transcript
- One-to-one with `interviewEvaluations`: Each interview may have one evaluation
- Many-to-many with `questions`: Each interview includes multiple questions from templates

**Common Queries**:

- Get interview by ID: `interviews.doc(interviewId).get()`
- Get interviews for an application: `interviews.where('applicationId', '==', applicationId).orderBy('stageIndex', 'asc').get()`
- Get upcoming interviews: `interviews.where('userId', '==', userId).where('status', '==', 'initiated').where('startTime', '>', now).get()`
- Get completed interviews without evaluations: `interviews.where('status', '==', 'completed').where('hasEvaluation', '==', false).get()`
- Get interviews by stage: `interviews.where('jobId', '==', jobId).where('stageName', '==', 'Technical').get()`
- Get automated interviews: `interviews.where('isAutomated', '==', true).get()`
- Get interviews by session ID: `interviews.where('sessionId', '==', sessionId).limit(1).get()`
- Get interview with evaluation: `interviews.where('evaluationId', '==', evaluationId).limit(1).get()`

### transcripts

**Purpose**: Store all conversation transcripts including intake calls and interview sessions with detailed conversation history to support AI-driven artifact generation.

**Fields**:

| Field                             | Type      | Description                                              | Required | Default           | Notes                                            |
| --------------------------------- | --------- | -------------------------------------------------------- | -------- | ----------------- | ------------------------------------------------ |
| id                                | string    | Unique identifier                                        | Yes      | auto-id           | Primary key                                      |
| applicationId                     | string    | Associated application ID                                | No       | null              | Foreign key to applications                      |
| jobId                             | string    | Associated job ID                                        | Yes      | -                 | Foreign key to jobs                              |
| type                              | string    | Transcript type (Intake, Interview)                      | Yes      | "Interview"       | Distinguishes between different transcript types |
| candidateId                       | string    | Candidate ID                                             | No       | null              | Foreign key to candidates                        |
| candidateName                     | string    | Candidate name                                           | No       | null              | For easier reference without joins               |
| userId                            | string    | Interviewer/user ID                                      | Yes      | -                 | Foreign key to users                             |
| companyId                         | string    | Reference to company                                     | Yes      | -                 | Foreign key to companies                         |
| sessionId                         | string    | ID of the interview session (deprecated)                 | No       | null              | **DEPRECATED**: No longer used             |
| interviewId                       | string    | Associated interview ID                                  | No       | null              | Foreign key to interviews                        |
| templateId                        | string    | ID of the interview template used                        | No       | null              | For linking to interview templates               |
| stageName                         | string    | Name of the interview stage                              | No       | null              | E.g., "Technical", "Cultural Fit"                |
| stageIndex                        | number    | Index of the stage in the interview process              | No       | null              | For ordering stages                              |
| messages                          | array     | **LIMITED**: Recent messages (max 100)             | Yes      | []                | **Size-bounded for performance**           |
| messages[].id                     | string    | Unique message identifier                                | Yes      | -                 | For message tracking                             |
| messages[].role                   | string    | Speaker role (user, assistant, candidate)                | Yes      | -                 | Identifies the speaker                           |
| messages[].content                | string    | Message content                                          | Yes      | -                 | The actual message text                          |
| messages[].timestamp              | number    | Message timestamp (milliseconds)                         | Yes      | -                 | When the message was sent                        |
| messageCount                      | number    | Total number of messages                                 | Yes      | 0                 | For quick reference                              |
| isMessagesTruncated               | boolean   | Whether messages array was truncated                     | Yes      | false             | **Performance indicator**                  |
| fullTranscriptUrl                 | string    | URL to complete transcript file                          | No       | null              | **For long conversations**                 |
| clientSecret                      | map       | Authentication information                               | No       | null              | For secure access                                |
| clientSecret.value                | string    | Secret token                                             | No       | null              | For authentication                               |
| clientSecret.expiresAt            | number    | Expiration timestamp                                     | No       | null              | When the secret expires                          |
| evaluationId                      | string    | Associated evaluation ID                                 | No       | null              | For linking to evaluations                       |
| hasEvaluation                     | boolean   | Whether an evaluation exists                             | Yes      | false             | Quick check for evaluation status                |
| evaluationSummary                 | map       | Summary of evaluation (if exists)                        | No       | null              | Quick access to evaluation results               |
| evaluationSummary.decision        | string    | Final decision (Go/No Go)                                | No       | null              | Hiring decision                                  |
| evaluationSummary.overallScore    | number    | Overall evaluation score                                 | No       | null              | Numeric score (typically 0-100)                  |
| evaluationSummary.confidence      | string    | Confidence level in evaluation                           | No       | null              | E.g., "High", "Medium", "Low"                    |
| evaluationSummary.summary         | string    | Brief summary of evaluation                              | No       | null              | Text summary of key points                       |
| evaluationSummary.minimumPassRate | number    | Minimum score to pass                                    | No       | null              | Threshold for passing                            |
| recordings                        | array     | Array of interview recording chunks                      | No       | []                | For audio/video recordings (placeholder)         |
| recordings[].type                 | string    | 'question' or 'answer'                                   | No       | null              | Type of recording segment (placeholder)         |
| recordings[].url                  | string    | URL to video/audio chunk                                 | No       | null              | Storage location (placeholder)                  |
| recordings[].questionId           | string    | Associated question ID (if applicable)                   | No       | null              | Foreign key to questions (placeholder)          |
| recordings[].duration             | number    | Duration of chunk (seconds)                              | No       | null              | Length of recording (placeholder)               |
| fullPlaybackUrl                   | string    | URL to full interview recording                          | No       | null              | For complete playback (placeholder)             |
| status                            | string    | Transcript status (inProgress, completed, failed, error) | Yes      | "inProgress"      | **Includes error states for reliability**  |
| summary                           | string    | AI-generated summary                                     | No       | null              | Brief overview of conversation                   |
| keyInsights                       | array     | Key insights extracted                                   | No       | []                | Important points from conversation               |
| metadata                          | map       | Additional metadata                                      | No       | {}                | Flexible container for extra data                |
| metadata.duration                 | number    | Duration in seconds                                      | No       | null              | Length of conversation                           |
| metadata.wordCount                | number    | Total word count                                         | No       | null              | Size metrics                                     |
| metadata.questionCount            | number    | Number of questions                                      | No       | null              | Question metrics                                 |
| startedAt                         | timestamp | When transcript started                                  | Yes      | serverTimestamp() | Start time                                       |
| completedAt                       | timestamp | When transcript completed                                | No       | null              | End time                                         |
| createdAt                         | timestamp | Creation timestamp                                       | Yes      | serverTimestamp() | When record was created                          |
| updatedAt                         | timestamp | Last update timestamp                                    | Yes      | serverTimestamp() | When record was last modified                    |
| isPublic                          | boolean   | Whether this is public                                   | Yes      | false             | Accessibility setting                            |
| processingStatus                  | string    | AI processing status                                     | No       | null              | For tracking AI processing                       |

**Indexes**:

- applicationId (ASC): For finding transcripts for an application
- jobId (ASC), type (ASC), createdAt (DESC): For getting transcripts by type for a job
- userId (ASC), createdAt (DESC): For getting transcripts created by a user
- status (ASC), updatedAt (ASC): For finding stalled transcripts
- candidateId (ASC), createdAt (DESC): For getting all transcripts for a candidate
- type (ASC), processingStatus (ASC): For monitoring AI processing status
- sessionId (ASC): For finding a transcript from a public session
- interviewId (ASC): For finding transcript for an interview
- templateId (ASC), createdAt (DESC): For finding transcripts using a specific template
- companyId (ASC), type (ASC), createdAt (DESC): For company-specific transcript queries
- hasEvaluation (ASC), createdAt (DESC): For finding transcripts with/without evaluations
- evaluationId (ASC): For finding transcript by evaluation ID
- stageName (ASC), jobId (ASC): For finding transcripts by interview stage for a job

**Relationships**:

- Many-to-one with `jobs`: Each transcript is associated with a job/role
- Many-to-one with `candidates`: Each transcript may involve a candidate
- Many-to-one with `users`: Each transcript is created by a user
- Many-to-one with `applications`: Each transcript may be linked to an application
- Many-to-one with `interviews`: Each transcript may be linked to an interview
- Many-to-one with `templates`: Each interview transcript may use a template
- One-to-one with `interviewEvaluations`: Each transcript may have an associated evaluation
- Many-to-many with `questions`: Each transcript may reference multiple questions

**Common Queries**:

- Get transcript by ID: `transcripts.doc(transcriptId).get()`
- Get transcripts for an application: `transcripts.where('applicationId', '==', applicationId).orderBy('createdAt', 'desc').get()`
- Get intake transcripts for a job: `transcripts.where('type', '==', 'Intake').where('jobId', '==', jobId).get()`
- Get interview transcripts for a job: `transcripts.where('type', '==', 'Interview').where('jobId', '==', jobId).get()`
- Get transcripts by stage: `transcripts.where('stageName', '==', stageName).where('jobId', '==', jobId).get()`
- Get recent transcripts by user: `transcripts.where('userId', '==', userId).orderBy('createdAt', 'desc').limit(10).get()`
- Get in-progress transcripts: `transcripts.where('status', '==', 'inProgress').get()`
- Get transcripts for a candidate: `transcripts.where('candidateId', '==', candidateId).orderBy('createdAt', 'desc').get()`
- Get transcripts with evaluations: `transcripts.where('has_evaluation', '==', true).get()`
- Get transcript by session ID: `transcripts.where('sessionId', '==', sessionId).limit(1).get()`
- Get transcripts by template: `transcripts.where('templateId', '==', templateId).get()`
- Get transcripts for AI processing: `transcripts.where('processingStatus', '==', 'Queued').get()`
- Get company transcripts: `transcripts.where('companyId', '==', companyId).where('type', '==', type).orderBy('createdAt', 'desc').get()`

### jobPostings

**Purpose**: Store published job postings for jobs. (Publicly visible to anyone if published, regardless of owner or company.)

**Fields**:

| Field                 | Type      | Description                    | Required | Default           | Notes                                         |
| --------------------- | --------- | ------------------------------ | -------- | ----------------- | --------------------------------------------- |
| id                    | string    | Unique identifier              | Yes      | auto-id           | Primary key                                   |
| jobId                 | string    | Associated job ID              | Yes      | -                 | Foreign key to jobs                           |
| userId                | string    | Creator user ID                | Yes      | -                 | Foreign key to users                          |
| companyId             | string    | Reference to company           | Yes      | -                 | Foreign key to companies (inherited from job) |
| aboutCompany          | string    | About the company introduction | No       | -                 |                                               |
| aboutTeam             | string    | About the team information     | No       | -                 |                                               |
| title                 | string    | Job title                      | Yes      | -                 |                                               |
| description           | string    | Full job description           | Yes      | -                 | This is the full jobPosting text              |
| shortDescription      | string    | Brief job summary              | No       | ""                |                                               |
| requirements          | array     | List of requirements           | No       | []                |                                               |
| responsibilities      | array     | List of responsibilities       | No       | []                |                                               |
| qualifications        | array     | List of qualifications         | No       | []                |                                               |
| benefits              | array     | List of benefits               | No       | []                |                                               |
| location              | map       | Job location                   | Yes      | -                 |                                               |
| location.city         | string    | City                           | No       | ""                |                                               |
| location.state        | string    | State/province                 | No       | ""                |                                               |
| location.country      | string    | Country                        | No       | ""                |                                               |
| location.remoteStatus | string    | Remote work status             | Yes      | "Remote"          |                                               |
| jobType               | string    | Employment type                | Yes      | "Full-time"       |                                               |
| salaryRange           | map       | Salary information             | No       | null              |                                               |
| salaryRange.min       | number    | Minimum salary                 | No       | null              |                                               |
| salaryRange.max       | number    | Maximum salary                 | No       | null              |                                               |
| salaryRange.currency  | string    | Currency code                  | No       | "USD"             |                                               |
| applicationUrl        | string    | URL for external applications  | No       | null              |                                               |
| departmentName        | string    | Department name                | No       | null              |                                               |
| isActive              | boolean   | Whether posting is active      | Yes      | true              |                                               |
| viewCount             | number    | Number of views                | Yes      | 0                 |                                               |
| applicationCount      | number    | Number of applications         | Yes      | 0                 |                                               |
| originalContent       | string    | Original source content        | No       | null              |                                               |
| publishedAt           | timestamp | Publication timestamp          | Yes      | serverTimestamp() |                                               |
| expiresAt             | timestamp | Expiration timestamp           | No       | null              |                                               |
| createdAt             | timestamp | Creation timestamp             | Yes      | serverTimestamp() |                                               |
| updatedAt             | timestamp | Last update timestamp          | Yes      | serverTimestamp() |                                               |
| seoTags               | array     | SEO keywords                   | No       | []                |                                               |
| isAiGenerated         | boolean   | Whether generated by AI        | Yes      | false             |                                               |

**Indexes**:

- jobId (ASC): For finding postings for a job
- userId (ASC), createdAt (DESC): For getting postings created by a user
- isActive (ASC), publishedAt (DESC): For getting active postings sorted by recency
- jobType (ASC), location.remoteStatus (ASC): For filtering postings by job type and remote status
- viewCount (DESC): For ranking postings by popularity
- location.city (ASC), isActive (ASC): For location-based job searches
- expiresAt (ASC), isActive (ASC): For finding soon-to-expire active postings

**Relationships**:

- Many-to-one with `jobs`: Each posting is for a specific job
- Many-to-one with `users`: Each posting is created by a user
- One-to-many with `applications`: A posting can receive multiple applications

**Common Queries**:

- Get posting by ID:`jobPostings.doc(postingId).get()`
- Get all postings for a job:`jobPostings.where('jobId', '==', jobId).get()`
- Get active postings:`jobPostings.where('isActive', '==', true).get()`
- Get recent postings:`jobPostings.where('isActive', '==', true).orderBy('publishedAt', 'desc').limit(20).get()`
- Get popular postings:`jobPostings.where('isActive', '==', true).orderBy('viewCount', 'desc').limit(10).get()`
- Get postings by location:`jobPostings.where('location.city', '==', city).where('isActive', '==', true).get()`
- Get remote job postings:`jobPostings.where('location.remoteStatus', '==', 'Remote').where('isActive', '==', true).get()`

**Advanced Candidate-Facing Queries & Filters:**

Candidates (including unauthenticated users) should be able to search and filter jobs with the following queries:

- **Search jobs by title (case-insensitive, partial match):**

  - Firestore does not support native full-text search. Use a third-party integration (e.g., Algolia, MeiliSearch) or store normalized keywords/array fields for prefix search.
  - Example:`jobs.where('isPublished', '==', true).where('titleKeywords', 'array-contains', searchTerm).get()`
- **Filter jobs by required skills:**

  - `jobs.where('isPublished', '==', true).where('requiredSkills.<skill>', '==', true).get()`
  - For multiple skills: Use multiple `.where()` or filter client-side after fetching.
- **Filter jobs by key responsibilities:**

  - If responsibilities are stored as an array, use:`jobs.where('isPublished', '==', true).where('keyResponsibilities', 'array-contains', responsibility).get()`
- **Filter jobs by posted date (recent jobs):**

  - `jobs.where('isPublished', '==', true).orderBy('publishedAt', 'desc').limit(20).get()`
  - To filter jobs posted after a certain date:`.where('publishedAt', '>=', someTimestamp)`
- **Filter jobs by location (city):**

  - `jobs.where('isPublished', '==', true).where('location.city', '==', cityName).get()`
- **Filter jobs by job type:**

  - `jobs.where('isPublished', '==', true).where('jobType', '==', jobType).get()`
- **Filter jobs by remote status:**

  - `jobs.where('isPublished', '==', true).where('location.remoteStatus', '==', remoteStatus).get()`
- **Combined filters:**

  - Example: Get all published remote software engineering jobs in New York posted in the last 30 days:
    ```js
    jobs
      .where('isPublished', '==', true)
      .where('location.remoteStatus', '==', 'Remote')
      .where('location.city', '==', 'New York')
      .where('jobType', '==', 'Full-time')
      .where('publishedAt', '>=', THIRTY_DAYS_AGO)
      .get()
    ```

**Required Composite Indexes:**

- `isPublished (ASC), publishedAt (DESC)` – for recent published jobs
- `isPublished (ASC), location.city (ASC), jobType (ASC), publishedAt (DESC)` – for multi-filtered queries
- `isPublished (ASC), requiredSkills.<skill> (ASC)` – for skill-based search
- `isPublished (ASC), keyResponsibilities (ASC)` – for responsibility-based search
- Add additional indexes as needed for common filter combinations

**Public/Unauthenticated Access (Security Rules):**

- Allow read access to jobs where `isPublished == true` for unauthenticated users:
  ```js
  match /jobs/{jobId} {
    allow read: if resource.data.isPublished == true;
    // ... other rules for authenticated users
  }
  ```
- Never expose unpublished or sensitive fields to unauthenticated users.
- Consider field masking or custom DTOs in API if exposing via backend.

**Best Practices:**

- Store normalized/keyword arrays for text search fields (e.g.,`title_keywords`).
- Use third-party search for advanced full-text or fuzzy matching.
- Regularly review and update indexes for query performance.
- Ensure frontend uses paginated queries for scalability.

---

### Sensitive Fields: Do Not Expose to Candidates

The following fields in the `jobs` collection are considered sensitive and should NOT be exposed to candidates (especially unauthenticated users):

- `userId`: Internal user reference (job creator)
- `hiringManagerId`: Internal user reference (hiring manager)
- `hiringManagerContact`: Personal/contact information
- `departmentId`: Internal department reference
- `priority`: Internal job priority
- `status`: Internal workflow status (Intake, Sourcing, etc.)
- `createdAt`,`updatedAt`: Internal timestamps (use only `publishedAt` for candidates)
- `isPublished`: Used for filtering, not for display
- `team`,`teamDynamic`,`keyStakeholders`: If containing internal notes or names not meant for public view
- `interviewProcess[].customInstructions`: May contain confidential interview details
- `salary`,`benefits`: Only show if intended to be public
- Any additional internal notes or fields not meant for public consumption

#### Candidate-Visible Fields (Recommended)

Only expose the following fields to candidates:

- `title`
- `summary`
- `jobType`
- `location` (city, remote status)
- `requiredSkills`,`preferredSkills`
- `keyResponsibilities` (if not sensitive)
- `experience` (minimum/preferred)
- `education` (degree/field)
- `salary` (if public)
- `benefits` (if public)
- `interviewProcess` (general stages, not custom/internal instructions)
- `publishedAt`

#### Security Notes

- Use allow-lists (not block-lists) for fields exposed to candidates.
- Use DTOs or custom API responses to control what is sent to the frontend.
- Never expose user IDs, contact info, or internal workflow/status fields to unauthenticated users.
- Review security rules and API responses regularly to ensure compliance.

---

## Pagination Strategy

### Comprehensive Cursor-Based Pagination for All Large Collections

**Replaces**: Single-document `applicationsView` collection with unbounded arrays

**Core Implementation Pattern**:

```javascript
// Standard page size for all collections
const PAGE_SIZE = 20;

// Generic pagination function
const paginateCollection = async (collection, filters = {}, orderBy = 'createdAt', orderDirection = 'desc', cursor = null) => {
  let query = collection;

  // Apply filters
  Object.entries(filters).forEach(([field, value]) => {
    query = query.where(field, '==', value);
  });

  // Apply ordering
  query = query.orderBy(orderBy, orderDirection);

  // Apply cursor for subsequent pages
  if (cursor) {
    query = query.startAfter(cursor);
  }

  // Apply limit
  query = query.limit(PAGE_SIZE);

  return await query.get();
};
```

### Collection-Specific Pagination Implementations

#### Applications Pagination (EXISTING - OPTIMIZED)

```javascript
// First page
const firstPage = await applications
  .where('companyId', '==', companyId)
  .orderBy('submittedAt', 'desc')
  .limit(PAGE_SIZE)
  .get();

// Subsequent pages using cursor
const nextPage = await applications
  .where('companyId', '==', companyId)
  .orderBy('submittedAt', 'desc')
  .startAfter(lastDocumentFromPreviousPage)
  .limit(PAGE_SIZE)
  .get();

// With filtering
const filteredPage = await applications
  .where('companyId', '==', companyId)
  .where('status', '==', 'interview')
  .orderBy('submittedAt', 'desc')
  .limit(PAGE_SIZE)
  .get();
```

#### Candidates Pagination (NEW)

```javascript
// All candidates with pagination
const candidatesPage = await candidates
  .orderBy('createdAt', 'desc')
  .limit(PAGE_SIZE)
  .get();

// Candidates by experience level
const experiencedCandidates = await candidates
  .where('yearsOfExperience', '>=', 5)
  .orderBy('yearsOfExperience', 'desc')
  .limit(PAGE_SIZE)
  .get();

// Candidates by skills (requires array-contains-any)
const skilledCandidates = await candidates
  .where('skills', 'array-contains-any', ['React', 'Node.js'])
  .orderBy('createdAt', 'desc')
  .limit(PAGE_SIZE)
  .get();
```

#### Jobs Pagination (NEW)

```javascript
// Company jobs with pagination
const companyJobs = await jobs
  .where('companyId', '==', companyId)
  .orderBy('updatedAt', 'desc')
  .limit(PAGE_SIZE)
  .get();

// Published jobs for public access
const publicJobs = await jobs
  .where('isPublished', '==', true)
  .orderBy('publishedAt', 'desc')
  .limit(PAGE_SIZE)
  .get();

// Jobs by status
const activeJobs = await jobs
  .where('companyId', '==', companyId)
  .where('status', 'not-in', ['closed', 'rejected'])
  .orderBy('updatedAt', 'desc')
  .limit(PAGE_SIZE)
  .get();
```

#### Interviews/Transcripts Pagination (NEW)

```javascript
// Company interviews with pagination
const companyInterviews = await interviews
  .where('companyId', '==', companyId)
  .orderBy('createdAt', 'desc')
  .limit(PAGE_SIZE)
  .get();

// Transcripts by type
const interviewTranscripts = await transcripts
  .where('companyId', '==', companyId)
  .where('type', '==', 'Interview')
  .orderBy('createdAt', 'desc')
  .limit(PAGE_SIZE)
  .get();
```

## Public Job Search Strategy

### Scalable Public Job Access

**Challenge**: Serving potentially thousands of published jobs to unauthenticated users efficiently.

**Solution**: Optimized query patterns with proper indexing and pagination.

#### Public Job Search Implementation

```javascript
// Basic public job search with pagination
const getPublicJobs = async (cursor = null, filters = {}) => {
  let query = jobs.where('isPublished', '==', true);

  // Apply optional filters
  if (filters.jobType) {
    query = query.where('jobType', '==', filters.jobType);
  }
  if (filters.remoteStatus) {
    query = query.where('location.remoteStatus', '==', filters.remoteStatus);
  }
  if (filters.city) {
    query = query.where('location.city', '==', filters.city);
  }

  // Order by publication date (most recent first)
  query = query.orderBy('publishedAt', 'desc');

  // Apply cursor for pagination
  if (cursor) {
    query = query.startAfter(cursor);
  }

  // Limit results for performance
  query = query.limit(20);

  return await query.get();
};

// Search by skills (requires denormalized skill fields)
const searchJobsBySkills = async (skills, cursor = null) => {
  return await jobs
    .where('isPublished', '==', true)
    .where('requiredSkills', 'array-contains-any', skills)
    .orderBy('publishedAt', 'desc')
    .startAfter(cursor)
    .limit(20)
    .get();
};

// Recent jobs (most common query)
const getRecentPublicJobs = async (cursor = null) => {
  return await jobs
    .where('isPublished', '==', true)
    .orderBy('publishedAt', 'desc')
    .startAfter(cursor)
    .limit(20)
    .get();
};
```

#### Required Indexes for Public Job Search

```javascript
// Essential compound indexes for public job queries
const publicJobIndexes = [
  // Basic published job pagination
  {
    "collectionGroup": "jobs",
    "fields": [
      {"fieldPath": "isPublished", "order": "ASCENDING"},
      {"fieldPath": "publishedAt", "order": "DESCENDING"}
    ]
  },

  // Job type filtering
  {
    "collectionGroup": "jobs",
    "fields": [
      {"fieldPath": "isPublished", "order": "ASCENDING"},
      {"fieldPath": "jobType", "order": "ASCENDING"},
      {"fieldPath": "publishedAt", "order": "DESCENDING"}
    ]
  },

  // Location-based filtering
  {
    "collectionGroup": "jobs",
    "fields": [
      {"fieldPath": "isPublished", "order": "ASCENDING"},
      {"fieldPath": "location.city", "order": "ASCENDING"},
      {"fieldPath": "publishedAt", "order": "DESCENDING"}
    ]
  },

  // Remote status filtering
  {
    "collectionGroup": "jobs",
    "fields": [
      {"fieldPath": "isPublished", "order": "ASCENDING"},
      {"fieldPath": "location.remoteStatus", "order": "ASCENDING"},
      {"fieldPath": "publishedAt", "order": "DESCENDING"}
    ]
  },

  // Combined location and job type
  {
    "collectionGroup": "jobs",
    "fields": [
      {"fieldPath": "isPublished", "order": "ASCENDING"},
      {"fieldPath": "location.city", "order": "ASCENDING"},
      {"fieldPath": "jobType", "order": "ASCENDING"},
      {"fieldPath": "publishedAt", "order": "DESCENDING"}
    ]
  }
];
```

#### Performance Optimizations

1. **Caching Strategy** (Future Implementation):

   - Cache frequently accessed public job lists
   - Implement CDN for static job data
   - Use Redis for hot job searches
2. **Rate Limiting for Public API**:

   ```javascript
   // Basic rate limiting for unauthenticated users
   const rateLimitConfig = {
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100, // limit each IP to 100 requests per windowMs
     message: 'Too many requests from this IP'
   };
   ```
3. **Optimized Security Rules for Public Access**:

   ```javascript
   // Allow public read access to published jobs only
   match /jobs/{jobId} {
     allow read: if resource.data.isPublished == true ||
                    hasCompanyAccess(resource.data.companyId);
     allow write: if hasCompanyAccess(resource.data.companyId);
   }
   ```

#### Field Exposure Control for Public Jobs

**Public-Safe Fields** (exposed to unauthenticated users):

- `title`, `summary`, `jobType`
- `location.city`, `location.remoteStatus`
- `requiredSkills`, `preferredSkills`
- `keyResponsibilities` (if not sensitive)
- `experience.minimum`, `experience.preferred`
- `education.degree`, `education.field`
- `publishedAt`

**Restricted Fields** (company-only):

- `userId`, `companyId`, `hiringManagerId`
- `status`, `priority`, `createdAt`, `updatedAt`
- `interviewProcess.customInstructions`
- Internal notes and sensitive data

## Performance-Critical Indexes

### Multi-Tenant Compound Indexes (ESSENTIAL)

| Collection             | Fields                                                                       | Purpose                                               |
| ---------------------- | ---------------------------------------------------------------------------- | ----------------------------------------------------- |
| **applications** | **companyId (ASC), submittedAt (DESC)**                                | **Primary pagination for company applications** |
| **applications** | **companyId (ASC), status (ASC), submittedAt (DESC)**                  | **Status-filtered pagination**                  |
| **applications** | **companyId (ASC), jobId (ASC), submittedAt (DESC)**                   | **Job-specific pagination**                     |
| **applications** | **companyId (ASC), resumeDecision (ASC), submittedAt (DESC)**          | **Resume decision filtering**                   |
| **applications** | **companyId (ASC), latestInterviewDecision (ASC), submittedAt (DESC)** | **Interview decision filtering**                |
| **applications** | **companyId (ASC), jobTeam (ASC), submittedAt (DESC)**                 | **Team-based filtering**                        |
| **applications** | **companyId (ASC), resumeScore (DESC)**                                | **Resume score ranking**                        |
| **applications** | **companyId (ASC), latestInterviewScore (DESC)**                       | **Interview score ranking**                     |
| dashboards             | companyId (ASC), updatedAt (DESC)                                            | Recent updates                                        |
| jobs                   | companyId (ASC), status (ASC), updatedAt (DESC)                              | Multi-tenant job queries                              |
| interviews             | companyId (ASC), status (ASC), startTime (ASC)                               | Multi-tenant interview queries                        |
| interviewEvaluations   | companyId (ASC), metadata.jobId (ASC), evaluationSummary.overallScore (DESC) | Multi-tenant evaluation queries                       |

### Simple Indexes

| Collection   | Field          | Order | Purpose                           |
| ------------ | -------------- | ----- | --------------------------------- |
| users        | email          | ASC   | Email lookup                      |
| users        | companyId      | ASC   | Company user lookup               |
| dashboards   | companyId      | ASC   | Company dashboard lookup          |
| jobs         | userId         | ASC   | Get user's jobs                   |
| jobs         | status         | ASC   | Filter jobs by status             |
| jobs         | isPublished    | ASC   | Get published jobs                |
| applications | candidateId    | ASC   | Get applications for a candidate  |
| applications | candidateEmail | ASC   | Email-based application lookup    |
| candidates   | email          | ASC   | Find candidates by email          |
| interviews   | applicationId  | ASC   | Get interviews for an application |
| interviews   | sessionId      | ASC   | Find interview from session ID    |
| transcripts  | interviewId    | ASC   | Find transcript for an interview  |

### Compound Indexes

| Collection | Fields                            | Purpose        |
| ---------- | --------------------------------- | -------------- |
| dashboards | companyId (ASC), updatedAt (DESC) | Recent updates |

**Note**: Pagination-first design with server-side filtering reduces complexity.

| jobs                    | userId (ASC), status (ASC)                                                   | Get user's jobs by status                           |
| jobs                    | userId (ASC), updatedAt (DESC)                                               | Get user's jobs by recency                          |
| jobs                    | isPublished (ASC), updatedAt (DESC)                                          | Get recent published jobs                           |
| jobs                    | companyId (ASC), status (ASC), updatedAt (DESC)                              | Multi-tenant job queries                            |
| applications            | companyId (ASC), jobId (ASC), status (ASC), submittedAt (DESC)               | Multi-tenant application queries                    |
| interviews              | companyId (ASC), status (ASC), startTime (ASC)                               | Multi-tenant interview queries                      |
| interviewEvaluations    | companyId (ASC), metadata.jobId (ASC), evaluationSummary.overallScore (DESC) | Multi-tenant evaluation queries                     |
| templates               | jobId (ASC), isDefault (ASC)                                                 | Find default template for job                       |
| templates               | isPublic (ASC), usageCount (DESC)                                            | Find popular public templates                       |
| questions               | templateId (ASC), sequence (ASC)                                             | Get ordered questions                               |
| criteria                | templateId (ASC), weight (DESC)                                              | Get criteria by importance                          |
| applications            | jobId (ASC), status (ASC), submittedAt (DESC)                                | Filter applications by status and recency           |
| applications            | jobId (ASC), overallScore (DESC)                                             | Rank applications by score                          |
| candidates              | skills (ARRAY_CONTAINS_ANY), yearsOfExperience (DESC)                        | Find candidates with specific skills and experience |
| resumeEvaluations       | jobId (ASC), overallScore (DESC)                                             | Rank resume evaluations by score                    |
| resumeEvaluations       | evaluatorId (ASC), createdAt (DESC)                                          | Get user's recent resume evaluations                |
| interviews              | userId (ASC), startTime (ASC)                                                | Get user's upcoming interviews                      |
| interviews              | status (ASC), startTime (ASC)                                                | Find upcoming interviews by status                  |
| interviews              | jobId (ASC), stageName (ASC)                                                 | Get interviews by stage for a job                   |
| interviews              | applicationId (ASC), stageIndex (ASC)                                        | Get interviews for an application in order          |
| transcripts             | jobId (ASC), type (ASC), createdAt (DESC)                                    | Get recent transcripts by type                      |
| transcripts             | interviewId (ASC), status (ASC)                                              | Get transcript status for an interview              |
| jobPostings             | isActive (ASC), publishedAt (DESC)                                           | Get recent active postings                          |
| jobPostings             | location.city (ASC), isActive (ASC)                                          | Location-based job search                           |

## Optimized Query Patterns

### User Queries

- Get user profile:`users.doc(userId).get()`
- Authenticate user:`users.where('email', '==', email).limit(1).get()`
- Get user's jobs:`jobs.where('userId', '==', userId).get()`
- Get company's jobs:`jobs.where('companyId', '==', userCompanyId).get()`

### Company Dashboard Management - **OPTIMIZED**

- Get dashboard: `dashboards.doc(companyId).get()`
- Real-time: `dashboards.doc(companyId).onSnapshot(callback)`
- **Performance**: Single document with size-limited arrays (5 items max)

### Company Applications Management - **COMPLETELY REDESIGNED**

```javascript
// Page 1: First 20 applications
applications.where('companyId', '==', companyId)
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .get()

// Filtered by status
applications.where('companyId', '==', companyId)
  .where('status', '==', 'interview')
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .get()

// Filtered by resume decision
applications.where('companyId', '==', companyId)
  .where('resumeDecision', '==', 'Pass')
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .get()

// Real-time on paginated data
applications.where('companyId', '==', companyId)
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .onSnapshot(callback)
```

### Job Management

- Get job details:`jobs.doc(jobId).get()`
- Get active jobs:`jobs.where('companyId', '==', userCompanyId).where('status', 'not-in', ['closed', 'rejected']).get()`
- Get high-priority jobs:`jobs.where('companyId', '==', userCompanyId).where('priority', '==', 'High').get()`
- Get job templates:`templates.where('jobId', '==', jobId).get()`

### Template Management

- Get template details:`templates.doc(templateId).get()`
- Get template questions:`questions.where('templateId', '==', templateId).orderBy('sequence', 'asc').get()`
- Get template criteria:`criteria.where('templateId', '==', templateId).orderBy('sequence', 'asc').get()`
- Get default template:`templates.where('jobId', '==', jobId).where('isDefault', '==', true).limit(1).get()`

### Application Processing - **PERFORMANCE OPTIMIZED**

**Paginated Application Lists**:

```javascript
// Company applications (paginated)
applications.where('companyId', '==', companyId)
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .get()

// Job-specific applications (paginated)
applications.where('companyId', '==', companyId)
  .where('jobId', '==', jobId)
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .get()

// Status-filtered applications (paginated)
applications.where('companyId', '==', companyId)
  .where('status', '==', 'interview')
  .orderBy('submittedAt', 'desc')
  .limit(20)
  .get()

// Top applications by interview score
applications.where('companyId', '==', companyId)
  .orderBy('latestInterviewScore', 'desc')
  .limit(20)
  .get()
```

**Single Lookups**:

- Get candidate applications:`applications.where('candidateId', '==', candidateId).orderBy('submittedAt', 'desc').get()`
- Get application by email:`applications.where('candidateEmail', '==', email).get()`

### Candidate Management

- Get candidate profile:`candidates.doc(candidateId).get()`
- Find candidate by email:`candidates.where('email', '==', email).limit(1).get()`
- Find candidates with skills:`candidates.where('skills', 'array-contains-any', ['React', 'Node.js']).get()`
- Get top candidates:`candidates.orderBy('averageScore', 'desc').limit(20).get()`

### Evaluation Management

- Get resume evaluation details:`resumeEvaluations.doc(evaluationId).get()`
- Get interview evaluation details:`interviewEvaluations.doc(evaluationId).get()`
- Get resume evaluations for application:`resumeEvaluations.where('applicationId', '==', applicationId).orderBy('createdAt', 'desc').get()`
- Get interview evaluations for application:`interviewEvaluations.where('metadata.applicationId', '==', applicationId).orderBy('metadata.evaluatedAt', 'desc').get()`
- Get resume evaluations by user:`resumeEvaluations.where('evaluatorId', '==', userId).orderBy('createdAt', 'desc').get()`
- Get interview evaluations by user:`interviewEvaluations.where('metadata.evaluatedBy', '==', userId).orderBy('metadata.evaluatedAt', 'desc').get()`

### Interview Management

- Get interview details: `interviews.doc(interviewId).get()`
- Get upcoming interviews: `interviews.where('userId', '==', userId).where('status', '==', 'initiated').where('startTime', '>', now).orderBy('startTime', 'asc').get()`
- Get company's interviews: `interviews.where('companyId', '==', userCompanyId).get()`
- Get application interviews: `interviews.where('applicationId', '==', applicationId).orderBy('stageIndex', 'asc').get()`
- Get interviews by stage: `interviews.where('jobId', '==', jobId).where('stageName', '==', 'Technical').get()`
- Get interview by session ID: `interviews.where('sessionId', '==', sessionId).limit(1).get()`
- Get interview transcript: `transcripts.where('interviewId', '==', interviewId).limit(1).get()`
- Get interview with evaluation: `interviews.where('evaluationId', '==', evaluationId).limit(1).get()`

### Job Posting Management

- Get job posting:`jobPostings.doc(postingId).get()`
- Get job postings:`jobPostings.where('jobId', '==', jobId).get()`
- Get active postings:`jobPostings.where('isActive', '==', true).orderBy('publishedAt', 'desc').get()`
- Get popular postings:`jobPostings.where('isActive', '==', true).orderBy('viewCount', 'desc').limit(10).get()`

## Data Population Strategy

When creating new records, always populate companyId from the user's company:

```typescript
// Example: Creating a new job
const createJob = async (jobData, userId) => {
  const user = await users.doc(userId).get();
  const userCompanyId = user.data().companyId;

  const jobWithCompany = {
    ...jobData,
    userId,
    companyId: userCompanyId, // Always inherit from user
    createdAt: serverTimestamp()
  };

  return jobs.add(jobWithCompany);
};

// Example: Creating an application (candidate action)
const createApplication = async (applicationData, jobId) => {
  const job = await jobs.doc(jobId).get();
  const jobCompanyId = job.data().companyId;

  const applicationWithCompany = {
    ...applicationData,
    jobId,
    companyId: jobCompanyId, // Inherit from job
    submittedAt: serverTimestamp()
  };

  return applications.add(applicationWithCompany);
};
```

## Data Validation Rules

### Common Validation Rules

1. **Required Fields**: Ensure required fields are present and have valid values
2. **Data Types**: Validate correct data types for all fields
3. **Enumerated Values**: Ensure fields with enumerated values contain only valid options
4. **Email Format**: Validate email fields match email format
5. **URL Format**: Validate URL fields match URL format
6. **Numeric Ranges**: Ensure numeric fields are within valid ranges
7. **Date Validation**: Ensure date fields have valid values and relationships (e.g., startDate < endDate)
8. **Array Constraints**: Validate arrays have expected structures and sizes
9. **Company Consistency**: Ensure companyId is consistent across related documents

### Collection-Specific Validation

#### users

- Email must be valid and unique
- Required fields (email, fullName) must be non-empty
- accountType must be one of: "free", "pro", "enterprise"
- job must be one of: "admin", "user"

#### dashboards

- Required fields (id, companyId, summary, recentJobs, recentApplications) must be present
- id must match document ID and equal companyId
- companyId must exist in companies collection
- All counts must be non-negative integers
- recentJobs/recentApplications max 5 items (size-limited for performance)
- version must be positive integer

#### jobs

- Required fields (title, summary, userId) must be non-empty
- status must be one of defined enum values
- jobType must be one of defined enum values
- priority must be one of: "Low", "Normal", "High", "Urgent"
- location.remoteStatus must be one of: "Remote", "Hybrid", "On-site"
- interviewProcess must be a valid array with at least one stage

#### templates

- Required fields (name, jobId, userId) must be non-empty
- type must be one of: "Screening", "Technical", "Behavioral"
- duration must be a positive integer
- passCriteria.requiredScore must be between 0 and 100

#### questions

- Required fields (question, templateId, jobId) must be non-empty
- type must be one of: "Open-ended", "Multiple-choice", "Technical"
- difficulty must be one of: "Easy", "Medium", "Hard"
- sequence must be a non-negative integer

#### criteria

- Required fields (name, description, templateId, jobId) must be non-empty
- category must be one of: "Technical", "Behavioral", "Communication"
- weight must be between 0 and 100
- minScore must be between 1 and 5

#### applications

- Required fields (jobId, candidateId, userId) must be non-empty
- status must be one of defined enum values
- submittedAt must be a valid timestamp
- overallScore (if present) must be between 0 and 100

#### candidates

- Email must be valid and unique
- Required fields (email, fullName) must be non-empty
- yearsOfExperience (if present) must be a non-negative number
- education array items must have all required fields if present

#### resumeEvaluations

- Required fields (applicationId, jobId, candidateId, evaluatorId) must be non-empty
- scores must contain valid criterion IDs as keys
- scores values must be between 1 and 5
- overallScore must be between 0 and 100
- recommendation must be one of: "Strong Yes", "Yes", "Maybe", "No", "Strong No"

#### interviewEvaluations

- Required fields (metadata.applicationId, metadata.jobId, metadata.interviewId) must be non-empty
- evaluationSummary.overallScore must be between 0 and 100
- evaluationSummary.decision must be one of: "Go", "No Go", "Maybe"
- evaluationSummary.confidence must be one of: "Low", "Medium", "High"

#### interviews

- Required fields (jobId, userId) must be non-empty
- stageName must be one of defined enum values
- status must be one of: "initiated", "inProgress", "completed", "cancelled"
- If startTime is present, it must be a valid timestamp

#### transcripts

- Required fields (jobId, userId, messages) must be non-empty
- type must be one of: "Intake", "Interview"
- status must be one of: "inProgress", "completed", "failed", "error"
- Each message in messages array must have role, content, and timestamp
- messages array must not exceed 100 items for performance
- When status is "failed" or "error", processingStatus should indicate the failure reason

#### Array Size Validation Rules

To prevent unbounded array growth and document size issues:

```typescript
// Validation rules for array sizes
const ARRAY_SIZE_LIMITS = {
  'transcripts.messages': 100,
  'dashboards.recentJobs': 5,
  'dashboards.recentApplications': 5,
  'interviews.questions': 50,
  'jobs.keyResponsibilities': 20,
  'candidates.skills': 50,
  'candidates.education': 10,
  'candidates.workHistory': 20,
  'applications.tags': 10,
  'jobPostings.requirements': 20,
  'jobPostings.responsibilities': 20,
  'jobPostings.qualifications': 20,
  'jobPostings.benefits': 20,
  'jobPostings.seoTags': 10
};

// Validation function
const validateArraySizes = (data: any, collection: string): boolean => {
  for (const [fieldPath, limit] of Object.entries(ARRAY_SIZE_LIMITS)) {
    if (fieldPath.startsWith(collection + '.')) {
      const field = fieldPath.split('.')[1];
      if (data[field] && Array.isArray(data[field]) && data[field].length > limit) {
        throw new Error(`${field} array exceeds maximum size of ${limit} items`);
      }
    }
  }
  return true;
};
```

#### job_postings

- Required fields (jobId, userId, title, description) must be non-empty
- jobType must be one of defined enum values
- location.remoteStatus must be one of: "Remote", "Hybrid", "On-site"
- If salaryRange is present, min and max must be present and min <= max

### Validation Rules for Company Consistency

```typescript
// Ensure companyId consistency
const validateCompanyConsistency = (parentCompanyId: string, childCompanyId: string): boolean => {
  return parentCompanyId === childCompanyId;
};

// Ensure required fields
const validateRequiredFields = (data: any, requiredFields: string[]): boolean => {
  return requiredFields.every(field => data[field] != null && data[field] !== '');
};
```

### Essential Compound Indexes

```javascript
// Add these composite indexes to firestore.indexes.json
{
  "indexes": [
    // CRITICAL: Multi-tenant application pagination (PRIMARY USE CASE)
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "submittedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "status", "order": "ASCENDING"},
        {"fieldPath": "submittedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "jobId", "order": "ASCENDING"},
        {"fieldPath": "submittedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "resumeDecision", "order": "ASCENDING"},
        {"fieldPath": "submittedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "latestInterviewDecision", "order": "ASCENDING"},
        {"fieldPath": "submittedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "jobTeam", "order": "ASCENDING"},
        {"fieldPath": "submittedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "createdByName", "order": "ASCENDING"},
        {"fieldPath": "submittedAt", "order": "DESCENDING"}
      ]
    },

    // CRITICAL: Score-based application ranking
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "resumeScore", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "latestInterviewScore", "order": "DESCENDING"}
      ]
    },

    // CRITICAL: Email-based application lookups within company
    {
      "collectionGroup": "applications",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "candidateEmail", "order": "ASCENDING"}
      ]
    },

    // CRITICAL: Multi-tenant job queries
    {
      "collectionGroup": "jobs",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "status", "order": "ASCENDING"},
        {"fieldPath": "updatedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "jobs",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "isPublished", "order": "ASCENDING"},
        {"fieldPath": "updatedAt", "order": "DESCENDING"}
      ]
    },

    // CRITICAL: Public job search indexes
    {
      "collectionGroup": "jobs",
      "fields": [
        {"fieldPath": "isPublished", "order": "ASCENDING"},
        {"fieldPath": "publishedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "jobs",
      "fields": [
        {"fieldPath": "isPublished", "order": "ASCENDING"},
        {"fieldPath": "jobType", "order": "ASCENDING"},
        {"fieldPath": "publishedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "jobs",
      "fields": [
        {"fieldPath": "isPublished", "order": "ASCENDING"},
        {"fieldPath": "location.city", "order": "ASCENDING"},
        {"fieldPath": "publishedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "jobs",
      "fields": [
        {"fieldPath": "isPublished", "order": "ASCENDING"},
        {"fieldPath": "location.remoteStatus", "order": "ASCENDING"},
        {"fieldPath": "publishedAt", "order": "DESCENDING"}
      ]
    },

    // CRITICAL: Candidate pagination with experience filtering
    {
      "collectionGroup": "candidates",
      "fields": [
        {"fieldPath": "yearsOfExperience", "order": "ASCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "candidates",
      "fields": [
        {"fieldPath": "averageScore", "order": "DESCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },

    // Multi-tenant interview queries
    {
      "collectionGroup": "interviews",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "status", "order": "ASCENDING"},
        {"fieldPath": "startTime", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "interviews",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },

    // Multi-tenant transcript queries
    {
      "collectionGroup": "transcripts",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "type", "order": "ASCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },

    // Multi-tenant evaluation queries
    {
      "collectionGroup": "interviewEvaluations",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "metadata.jobId", "order": "ASCENDING"},
        {"fieldPath": "evaluationSummary.overallScore", "order": "DESCENDING"}
      ]
    },

    // Dashboard concurrency control
    {
      "collectionGroup": "dashboards",
      "fields": [
        {"fieldPath": "companyId", "order": "ASCENDING"},
        {"fieldPath": "version", "order": "ASCENDING"}
      ]
    },

    // Resume queries by candidate
    {
      "collectionGroup": "resumes",
      "fields": [
        {"fieldPath": "candidateId", "order": "ASCENDING"},
        {"fieldPath": "isPrimary", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "resumes",
      "fields": [
        {"fieldPath": "candidateId", "order": "ASCENDING"},
        {"fieldPath": "uploadedAt", "order": "DESCENDING"}
      ]
    }
  ]
}
```

## Security Rules

### Custom Claims Dependency

**CRITICAL**: Security rules depend on custom claims being properly set in Firebase Auth tokens.

```typescript
// Custom claims must be set when user signs up or company changes
const setUserCustomClaims = async (userId: string, companyId: string, userRole: string) => {
  await admin.auth().setCustomUserClaims(userId, {
    companyId,
    userRole
  });
};

// Call this function when:
// 1. User signs up and joins a company
// 2. User's company changes
// 3. User's role changes
```

### Optimized Base Rules (Minimal get() Calls)

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions - OPTIMIZED to minimize get() calls
    function isAuthenticated() {
      return request.auth != null;
    }

    // CRITICAL: Requires custom claims to be set properly
    function getUserCompanyId() {
      return request.auth.token.companyId;
    }

    function hasCompanyAccess(resourceCompanyId) {
      return isAuthenticated() && getUserCompanyId() == resourceCompanyId;
    }

    function isAdmin() {
      return request.auth.token.userRole == 'Admin';
    }

    // Fallback check using user document (only when custom claims fail)
    function isCompanyMemberFallback(resourceCompanyId) {
      return exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.companyId == resourceCompanyId;
    }

    // Enhanced company access with fallback
    function hasCompanyAccessWithFallback(resourceCompanyId) {
      return isAuthenticated() && (
        getUserCompanyId() == resourceCompanyId ||
        isCompanyMemberFallback(resourceCompanyId)
      );
    }

    // Companies - only admins can manage
    match /companies/{companyId} {
      allow read: if hasCompanyAccess(companyId) || isAdmin();
      allow write: if isAdmin();
    }

    // Company Dashboard - all company users can read
    match /dashboards/{companyId} {
      allow read: if hasCompanyAccess(companyId);
      allow write: if false; // Only Cloud Functions
    }

    // Users - can read own profile and company members
    match /users/{userId} {
      allow read: if isAuthenticated() && (
        request.auth.uid == userId ||
        hasCompanyAccess(resource.data.companyId) ||
        isAdmin()
      );
      allow create: if request.auth.uid == userId;
      allow update: if request.auth.uid == userId || isAdmin();
    }

    // Jobs - OPTIMIZED: company members can manage, public can view published
    match /jobs/{jobId} {
      allow read: if resource.data.isPublished == true ||
                     hasCompanyAccess(resource.data.companyId);
      allow write: if hasCompanyAccess(resource.data.companyId);
    }

    // Applications - OPTIMIZED: company members can manage, anyone can create
    match /applications/{applicationId} {
      allow read: if hasCompanyAccess(resource.data.companyId);
      allow create: if true; // Anyone can apply
      allow update: if hasCompanyAccess(resource.data.companyId);
    }

    // Candidates - OPTIMIZED: any authenticated user can view
    match /candidates/{candidateId} {
      allow read: if isAuthenticated();
      allow create: if true; // Anyone can create candidate profile
      allow update: if true; // Candidates can update their own info
    }

    // Resumes - OPTIMIZED: readable by authenticated users
    match /resumes/{resumeId} {
      allow read: if isAuthenticated();
      allow create: if true;
      allow update: if true;
    }

    // Company-scoped collections - OPTIMIZED: only company members can access
    match /templates/{templateId} {
      allow read, write: if hasCompanyAccess(resource.data.companyId);
    }

    match /questions/{questionId} {
      allow read, write: if hasCompanyAccess(resource.data.companyId);
    }

    match /criteria/{criterionId} {
      allow read, write: if hasCompanyAccess(resource.data.companyId);
    }

    match /interviews/{interviewId} {
      allow read: if resource.data.isPublic == true ||
                     hasCompanyAccess(resource.data.companyId);
      allow write: if hasCompanyAccess(resource.data.companyId);
    }

    match /transcripts/{transcriptId} {
      allow read: if resource.data.isPublic == true ||
                     hasCompanyAccess(resource.data.companyId);
      allow write: if hasCompanyAccess(resource.data.companyId);
    }

    match /interviewEvaluations/{evaluationId} {
      allow read, write: if hasCompanyAccess(resource.data.companyId);
    }

    match /resumeEvaluations/{evaluationId} {
      allow read, write: if hasCompanyAccess(resource.data.companyId);
    }

    // Job postings - OPTIMIZED: public read, company write
    match /jobPostings/{postingId} {
      allow read: if resource.data.isActive == true ||
                     hasCompanyAccess(resource.data.companyId);
      allow write: if hasCompanyAccess(resource.data.companyId);
    }

    // Array size validation rules
    match /transcripts/{transcriptId} {
      allow write: if hasCompanyAccess(resource.data.companyId) &&
                      (!request.resource.data.keys().hasAny(['messages']) ||
                       request.resource.data.messages.size() <= 100);
    }

    match /dashboards/{companyId} {
      allow write: if hasCompanyAccess(companyId) &&
                      (!request.resource.data.keys().hasAny(['recentJobs']) ||
                       request.resource.data.recentJobs.size() <= 5) &&
                      (!request.resource.data.keys().hasAny(['recentApplications']) ||
                       request.resource.data.recentApplications.size() <= 5);
    }
  }
}
```

### Collection-Specific Rules

#### users

```
match /users/{userId} {
  allow read: if isAuthenticated() && (isOwner(userId) || isAdmin());
  allow create: if isAuthenticated() && isOwner(userId);
  allow update: if isAuthenticated() && (isOwner(userId) || isAdmin());
  allow delete: if isAdmin();
}
```

#### jobs

```
match /jobs/{jobId} {
  allow read: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin() ||
    resource.data.isPublished == true
  );
  allow create: if isAuthenticated();
  allow update: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
  allow delete: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
}
```

#### templates

```
match /templates/{templateId} {
  allow read: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin() ||
    resource.data.isPublic == true ||
    isPublicJob(resource.data.jobId)
  );
  allow create: if isAuthenticated();
  allow update: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
  allow delete: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
}
```

#### questions and criteria

```
match /questions/{questionId} {
  allow read: if isAuthenticated() && (
    exists(/databases/$(database)/documents/templates/$(resource.data.templateId)) &&
    get(/databases/$(database)/documents/templates/$(resource.data.templateId)).data.userId == request.auth.uid ||
    get(/databases/$(database)/documents/templates/$(resource.data.templateId)).data.isPublic == true ||
    isAdmin()
  );
  allow write: if isAuthenticated() && (
    exists(/databases/$(database)/documents/templates/$(request.resource.data.templateId)) &&
    get(/databases/$(database)/documents/templates/$(request.resource.data.templateId)).data.userId == request.auth.uid ||
    isAdmin()
  );
}

match /criteria/{criterionId} {
  allow read: if isAuthenticated() && (
    exists(/databases/$(database)/documents/templates/$(resource.data.templateId)) &&
    get(/databases/$(database)/documents/templates/$(resource.data.templateId)).data.userId == request.auth.uid ||
    get(/databases/$(database)/documents/templates/$(resource.data.templateId)).data.isPublic == true ||
    isAdmin()
  );
  allow write: if isAuthenticated() && (
    exists(/databases/$(database)/documents/templates/$(request.resource.data.templateId)) &&
    get(/databases/$(database)/documents/templates/$(request.resource.data.templateId)).data.userId == request.auth.uid ||
    isAdmin()
  );
}
```

#### applications

```
match /applications/{applicationId} {
  allow read: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin() ||
    resource.data.isPublic == true
  );
  allow create: if true; // Allow anyone to create an application
  allow update: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
  allow delete: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
}
```

#### candidates

```
match /candidates/{candidateId} {
  allow read: if isAuthenticated() || resource.data.isPublic == true;
  allow create: if true; // Allow anyone to create a candidate
  allow update: if isAuthenticated() && (
    existsAfter(/databases/$(database)/documents/applications).where('candidateId', '==', candidateId).where('userId', '==', request.auth.uid).size() > 0 ||
    isAdmin()
  );
  allow delete: if isAdmin();
}
```

#### evaluations

```
match /evaluations/{evaluationId} {
  allow read: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin() ||
    resource.data.isPublic == true ||
    (
      existsAfter(/databases/$(database)/documents/jobs/$(resource.data.jobId)) &&
      get(/databases/$(database)/documents/jobs/$(resource.data.jobId)).data.userId == request.auth.uid
    )
  );
  allow create: if isAuthenticated();
  allow update: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin() ||
    (
      existsAfter(/databases/$(database)/documents/jobs/$(resource.data.jobId)) &&
      get(/databases/$(database)/documents/jobs/$(resource.data.jobId)).data.userId == request.auth.uid
    )
  );
  allow delete: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
}
```

#### interviews and transcripts

```
match /interviews/{interviewId} {
  allow read: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin() ||
    (
      exists(/databases/$(database)/documents/jobs/$(resource.data.jobId)) &&
      get(/databases/$(database)/documents/jobs/$(resource.data.jobId)).data.userId == request.auth.uid
    )
  );
  allow create: if isAuthenticated();
  allow update: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin() ||
    (
      exists(/databases/$(database)/documents/jobs/$(resource.data.jobId)) &&
      get(/databases/$(database)/documents/jobs/$(resource.data.jobId)).data.userId == request.auth.uid
    )
  );
  allow delete: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
}

match /transcripts/{transcriptId} {
  allow read: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin() ||
    (
      exists(/databases/$(database)/documents/jobs/$(resource.data.jobId)) &&
      get(/databases/$(database)/documents/jobs/$(resource.data.jobId)).data.userId == request.auth.uid
    )
  );
  allow create: if isAuthenticated();
  allow update: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin() ||
    (
      exists(/databases/$(database)/documents/jobs/$(resource.data.jobId)) &&
      get(/databases/$(database)/documents/jobs/$(resource.data.jobId)).data.userId == request.auth.uid
    )
  );
  allow delete: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
}
```

#### jobPostings

```
match /jobPostings/{postingId} {
  allow read: if true; // Public read access
  allow create: if isAuthenticated();
  allow update: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
  allow delete: if isAuthenticated() && (
    resource.data.userId == request.auth.uid ||
    isAdmin()
  );
}
```

## Data Transfer Objects

To optimize data transfer between the frontend and backend, below are the recommended Data Transfer Objects (DTOs) that include only the necessary fields for common operations. These DTOs reflect the new resume and resume evaluation structure, and should be used for efficient UI rendering and API responses:

### JobSummaryDTO

For job list views showing minimal information:

```typescript
interface JobSummaryDTO {
  id: string;
  title: string;
  status: string;
  priority: string;
  jobType: string;
  location: {
    city: string;
    remoteStatus: string;
  };
  isPublished: boolean;
  updatedAt: Timestamp;
  companyId: string; // Reference to company
}
```

### ApplicationListDTO

For application list views:

```typescript
interface ApplicationListDTO {
  id: string;
  candidateId: string;
  candidateName: string; // Denormalized from candidates
  status: string;
  submittedAt: Timestamp;
  resumeId: string; // References resumes collection
  resumeScore?: number; // Denormalized resume score
  resumeDecision?: string; // Denormalized resume decision
  latestInterviewScore?: number; // Denormalized interview score
  latestInterviewDecision?: string; // Denormalized interview decision
  hasPendingAction: boolean;
}
```

### InterviewEvaluationSummaryDTO

For interview evaluation list views:

```typescript
interface InterviewEvaluationSummaryDTO {
  id: string;
  candidateName: string; // Denormalized from candidates
  jobName: string; // Denormalized from jobs (formerly roleName)
  overallScore: number;
  decision: string; // Changed from recommendation to match collection
  evaluatedAt: Timestamp; // Changed from completedAt to match collection
}
```

### CandidateSearchDTO

For candidate search results:

```typescript
interface CandidateSearchDTO {
  id: string;
  fullName: string;
  email: string;
  currentPosition: string;
  currentCompany: string;
  skills: string[];
  yearsOfExperience: number;
  averageScore: number;
}
```

### ResumeDTO

For displaying or managing candidate resumes:

```typescript
interface ResumeDTO {
  id: string;
  candidateId: string;
  fileUrl: string;
  fileName: string;
  uploadedAt: Timestamp;
  fileType: string;
  fileSize: number;
  isPrimary: boolean;
  parsedText?: string;
  tags?: string[];
  metadata?: Record<string, any>;
  updatedAt: Timestamp;
}
```

### ResumeEvaluationDTO

For displaying or managing resume evaluations:

```typescript
interface ResumeEvaluationDTO {
  id: string;
  applicationId: string;
  resumeId: string;
  candidateId: string;
  evaluatorId: string;
  scores: Record<string, number>;
  overallScore: number;
  summary?: string;
  strengths?: string[];
  weaknesses?: string[];
  recommendation: string;
  status: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  notes?: string;
}
```

### CompanyDashboardDTO

```typescript
interface CompanyDashboardDTO {
  id: string; // companyId
  companyId: string;
  summary: {
    openJobsCount: number;
    activeApplicationsCount: number;
    interviewsThisWeekCount: number;
    finalistsCount: number;
    applicationStats: {
      totalApplicationsCount: number;
      pendingReviewCount: number;
      inInterviewCount: number;
      resumePassCount: number;
      resumeFailCount: number;
      interviewPassCount: number;
      interviewFailCount: number;
      avgResumeScore: number;
      avgLatestInterviewScore: number;
      applicationsThisWeek: number;
      applicationsThisMonth: number;
    };
  };
  recentJobs: CompanyJobDTO[];
  recentApplications: CompanyApplicationDTO[];
  updatedAt: Timestamp;
  version: number;
}

interface CompanyJobDTO {
  id: string;
  title: string;
  team: string;
  createdBy: string;
  createdByName: string;
  applicantsCount: number;
  status: string;
}



interface CompanyApplicationDTO {
  id: string;
  candidateName: string;
  jobTitle: string;
  submittedAt: Timestamp;
  status: string;
  resumeScore?: number;
  latestInterviewScore?: number;
}
```

### ApplicationListPaginatedDTO - **REPLACES CompanyApplicationsViewDTO**

```typescript
// Paginated application list response
interface ApplicationListPaginatedDTO {
  applications: ApplicationListItemDTO[];
  hasMore: boolean;
  lastDocument?: DocumentSnapshot;
  totalCount?: number; // Optional, from separate count query
}

interface ApplicationListItemDTO {
  id: string;
  candidateName: string;
  candidateEmail: string;
  submittedAt: Timestamp;
  status: string;
  jobId: string;
  jobTitle: string;
  jobTeam: string;
  jobDepartment?: string;
  createdByName: string;
  resumeScore?: number;
  resumeDecision?: string;
  latestInterviewScore?: number;
  latestInterviewDecision?: string;
}

// Filtering interface for paginated queries
interface ApplicationFilters {
  jobId?: string;
  jobTeam?: string;
  resumeDecision?: string;
  latestInterviewDecision?: string;
  createdByName?: string;
  status?: string;
}
```

These DTOs can be implemented through field selection in the Firebase queries, ensuring minimal data transfer while providing all necessary information for common UI operations.

## Cloud Functions Strategy

### Queue-Based Update Strategy

To prevent lost updates and performance issues, use Cloud Tasks instead of rate limiting:

```typescript
// Queue configuration
const QUEUE_CONFIG = {
  updateCompanyDashboard: {
    maxInstances: 5,
    scheduleDelaySeconds: 30, // Batch updates every 30 seconds
    dispatchDeadlineSeconds: 300, // 5 minute timeout
  },
  maintainDenormalizedFields: {
    maxInstances: 10,
    batchSize: 50,
  }
};

// Queue dashboard updates instead of dropping them
export const queueDashboardUpdate = functions.https.onCall(async (data) => {
  const { companyId } = data;

  // Use Cloud Tasks to queue the update
  const queue = getFunctions().taskQueue('updateDashboard');

  try {
    await queue.enqueue(
      { companyId },
      {
        scheduleDelaySeconds: QUEUE_CONFIG.updateCompanyDashboard.scheduleDelaySeconds,
        dispatchDeadlineSeconds: QUEUE_CONFIG.updateCompanyDashboard.dispatchDeadlineSeconds
      }
    );

    console.log(`Dashboard update queued for company: ${companyId}`);
    return { success: true };
  } catch (error) {
    console.error('Failed to queue dashboard update:', error);
    throw new functions.https.HttpsError('internal', 'Failed to queue update');
  }
});

// Process queued dashboard updates
export const processDashboardUpdate = functions.tasks.taskQueue({
  retryConfig: {
    maxAttempts: 3,
    minBackoffSeconds: 60,
  },
  rateLimits: {
    maxConcurrentDispatches: QUEUE_CONFIG.updateCompanyDashboard.maxInstances,
  }
}).onDispatch(async (req) => {
  const { companyId } = req.data;

  try {
    await updateDashboardData(companyId);
    console.log(`Dashboard updated successfully for company: ${companyId}`);
  } catch (error) {
    console.error(`Dashboard update failed for company ${companyId}:`, error);
    throw error; // Will trigger retry
  }
});

// Trigger function that queues updates instead of processing directly
export const onJobChange = functions.firestore
  .document('jobs/{jobId}')
  .onWrite(async (change, context) => {
    const jobData = change.after.exists ? change.after.data() : change.before.data();
    const companyId = jobData?.companyId;

    if (companyId) {
      // Queue the update instead of processing immediately
      await queueDashboardUpdate({ data: { companyId } });
    }
  });
```

### Functions

#### updateCompanyDashboard

- **Purpose**: Update size-limited company dashboard with aggregates
- **Parameters**: `companyId: string`
- **Logic**:
  - Calculate aggregate statistics from applications, jobs, interviews
  - Maintain only 5 most recent jobs and 5 most recent applications
  - Update single dashboard document
- **Performance**: Keeps dashboard under 50KB vs potential 1MB+

#### maintainDenormalizedFields

- **Purpose**: Keep denormalized fields in applications collection up-to-date with batching
- **Parameters**: `updates: Array<{applicationId: string, updates: object}>`
- **Logic**:
  - Process updates in batches of 50 to prevent timeouts
  - Update candidateName when candidate changes
  - Update jobTitle when job changes
  - Update resumeScore/resumeDecision when resume evaluation changes
  - Update latestInterviewScore/latestInterviewDecision when interview evaluation changes
  - Use Firestore batch writes for atomic updates
- **Performance**: Enables single-query application lists without timeout risks

```typescript
const maintainDenormalizedFields = async (updates: Array<{applicationId: string, updates: object}>) => {
  const BATCH_SIZE = 50;
  const batches = [];

  for (let i = 0; i < updates.length; i += BATCH_SIZE) {
    const batch = admin.firestore().batch();
    const batchUpdates = updates.slice(i, i + BATCH_SIZE);

    batchUpdates.forEach(({ applicationId, updates }) => {
      const applicationRef = admin.firestore().collection('applications').doc(applicationId);
      batch.update(applicationRef, {
        ...updates,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    });

    batches.push(batch.commit());
  }

  await Promise.all(batches);
  console.log(`Updated ${updates.length} applications in ${batches.length} batches`);
};
```

### Trigger Functions

#### updateDashboardOnJobChange

- **Trigger**: `functions.firestore.document('jobs/{jobId}').onWrite`
- **Rate Limiting**: Max 5 instances, 30-second cooldown
- **Logic**: Get companyId, call `updateCompanyDashboard(companyId)` with rate limiting

#### updateDashboardOnApplicationChange

- **Trigger**: `functions.firestore.document('applications/{applicationId}').onWrite`
- **Rate Limiting**: Max 5 instances, 30-second cooldown
- **Logic**: Get companyId, call `updateCompanyDashboard(companyId)` with rate limiting

#### updateApplicationDenormalization

- **Trigger**: Multiple triggers for maintaining denormalized fields
- **Rate Limiting**: Max 10 instances, batch processing of 50 items

  - `candidates/{candidateId}.onUpdate` → Update candidateName in applications
  - `jobs/{jobId}.onUpdate` → Update jobTitle in applications
  - `resumeEvaluations/{evaluationId}.onWrite` → Update resume fields
  - `interviewEvaluations/{evaluationId}.onWrite` → Update interview fields

#### Performance Safeguards

- **Transcript Size Management**: Automatically truncate messages array to 100 items, store full transcript in Cloud Storage
- **Dashboard Size Limits**: Maintain only 5 recent items in arrays
- **Array Size Enforcement**: Implement Cloud Function validation for all array size limits
- **Batch Processing**: Process denormalization updates in batches to prevent timeouts
- **Error Handling**: Implement retry logic with exponential backoff for failed updates

```typescript
// Cloud Function to enforce array size limits
export const enforceArraySizeLimits = functions.firestore
  .document('{collection}/{docId}')
  .onWrite(async (change, context) => {
    const { collection } = context.params;
    const data = change.after.exists ? change.after.data() : null;

    if (!data) return;

    try {
      validateArraySizes(data, collection);
    } catch (error) {
      console.error(`Array size validation failed for ${collection}:`, error);
      // Optionally truncate arrays or reject the write
      throw new functions.https.HttpsError('invalid-argument', error.message);
    }
  });

// Dashboard array maintenance
export const maintainDashboardArrays = functions.firestore
  .document('dashboards/{companyId}')
  .onWrite(async (change, context) => {
    const data = change.after.data();
    if (!data) return;

    let needsUpdate = false;
    const updates: any = {};

    // Enforce 5-item limit on recentJobs
    if (data.recentJobs && data.recentJobs.length > 5) {
      updates.recentJobs = data.recentJobs
        .sort((a, b) => b.createdAt.toMillis() - a.createdAt.toMillis())
        .slice(0, 5);
      needsUpdate = true;
    }

    // Enforce 5-item limit on recentApplications
    if (data.recentApplications && data.recentApplications.length > 5) {
      updates.recentApplications = data.recentApplications
        .sort((a, b) => b.submittedAt.toMillis() - a.submittedAt.toMillis())
        .slice(0, 5);
      needsUpdate = true;
    }

    if (needsUpdate) {
      await change.after.ref.update(updates);
    }
  });
```

## Concurrency Control Implementation

### Dashboard Optimistic Locking

**Problem**: Multiple concurrent updates to dashboard could cause data inconsistencies.

**Solution**: Implement optimistic concurrency control using version field.

#### Implementation

```typescript
// Dashboard update with optimistic locking
const updateDashboard = async (companyId: string, updates: any) => {
  const dashboardRef = db.collection('dashboards').doc(companyId);

  return db.runTransaction(async (transaction) => {
    const doc = await transaction.get(dashboardRef);

    if (!doc.exists) {
      throw new Error('Dashboard not found');
    }

    const currentData = doc.data();
    const currentVersion = currentData?.version || 0;

    // Update with incremented version
    transaction.update(dashboardRef, {
      ...updates,
      version: currentVersion + 1,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    return { success: true, newVersion: currentVersion + 1 };
  });
};

// Client-side optimistic update handling
const handleDashboardUpdate = async (companyId: string, updates: any, expectedVersion: number) => {
  try {
    const result = await updateDashboard(companyId, updates);
    console.log('Dashboard updated successfully:', result);
    return result;
  } catch (error) {
    if (error.code === 'failed-precondition') {
      // Version conflict - refresh and retry
      console.log('Version conflict detected, refreshing dashboard...');
      const freshDashboard = await getDashboard(companyId);
      throw new Error('Dashboard was updated by another user. Please refresh and try again.');
    }
    throw error;
  }
};
```

#### Monitoring Concurrent Updates

```typescript
// Cloud Function to monitor dashboard update conflicts
export const monitorDashboardConflicts = functions.firestore
  .document('dashboards/{companyId}')
  .onWrite(async (change, context) => {
    const { companyId } = context.params;
    const before = change.before.data();
    const after = change.after.data();

    if (before && after) {
      const versionJump = after.version - before.version;

      if (versionJump > 1) {
        console.warn(`Dashboard version jump detected for company ${companyId}: ${before.version} -> ${after.version}`);

        // Log potential conflict for monitoring
        await admin.firestore().collection('_monitoring').add({
          type: 'dashboard_version_conflict',
          companyId,
          previousVersion: before.version,
          newVersion: after.version,
          timestamp: admin.firestore.FieldValue.serverTimestamp()
        });
      }
    }
  });
```

## Error Handling and Monitoring

### Custom Claims Monitoring

**Problem**: Security rules depend on custom claims being set correctly. Missing claims cause access issues.

**Solution**: Implement monitoring and fallback mechanisms.

#### Custom Claims Management

```typescript
// Set custom claims with error handling
const setUserCustomClaims = async (userId: string, companyId: string, userRole: string) => {
  try {
    await admin.auth().setCustomUserClaims(userId, {
      companyId,
      userRole,
      lastUpdated: Date.now()
    });

    console.log(`Custom claims set for user ${userId}: companyId=${companyId}, role=${userRole}`);

    // Log successful claim update for monitoring
    await admin.firestore().collection('_monitoring').add({
      type: 'custom_claims_updated',
      userId,
      companyId,
      userRole,
      timestamp: admin.firestore.FieldValue.serverTimestamp()
    });

  } catch (error) {
    console.error(`Failed to set custom claims for user ${userId}:`, error);

    // Log failed claim update for monitoring
    await admin.firestore().collection('_monitoring').add({
      type: 'custom_claims_error',
      userId,
      companyId,
      userRole,
      error: error.message,
      timestamp: admin.firestore.FieldValue.serverTimestamp()
    });

    throw error;
  }
};

// Monitor users with missing custom claims
export const monitorMissingClaims = functions.pubsub
  .schedule('every 1 hours')
  .onRun(async (context) => {
    const usersSnapshot = await admin.firestore()
      .collection('users')
      .where('companyId', '!=', null)
      .get();

    const usersWithMissingClaims = [];

    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      const userId = userDoc.id;

      try {
        const userRecord = await admin.auth().getUser(userId);
        const claims = userRecord.customClaims || {};

        if (!claims.companyId || claims.companyId !== userData.companyId) {
          usersWithMissingClaims.push({
            userId,
            expectedCompanyId: userData.companyId,
            actualCompanyId: claims.companyId,
            userRole: userData.userRole
          });
        }
      } catch (error) {
        console.error(`Error checking claims for user ${userId}:`, error);
      }
    }

    if (usersWithMissingClaims.length > 0) {
      console.warn(`Found ${usersWithMissingClaims.length} users with missing/incorrect custom claims`);

      // Log for monitoring
      await admin.firestore().collection('_monitoring').add({
        type: 'missing_custom_claims_detected',
        count: usersWithMissingClaims.length,
        users: usersWithMissingClaims,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });

      // Auto-fix claims for users with missing claims
      for (const user of usersWithMissingClaims) {
        try {
          await setUserCustomClaims(user.userId, user.expectedCompanyId, user.userRole);
          console.log(`Auto-fixed custom claims for user ${user.userId}`);
        } catch (error) {
          console.error(`Failed to auto-fix claims for user ${user.userId}:`, error);
        }
      }
    }
  });
```

#### Enhanced Security Rules with Fallback

```javascript
// Enhanced security rules with fallback mechanisms
function hasCompanyAccess(resourceCompanyId) {
  return isAuthenticated() && (
    getUserCompanyId() == resourceCompanyId ||
    isCompanyMember(resourceCompanyId) // Fallback check
  );
}

// Fallback check using user document
function isCompanyMember(resourceCompanyId) {
  return exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.companyId == resourceCompanyId;
}

// Optimized function to minimize get() calls
function getUserCompanyId() {
  // Primary: Use custom claims (fast)
  return request.auth.token.companyId;
}
```

#### Transcript Overflow Management

**Problem**: Transcript messages can exceed 100-item limit, causing data loss.

**Solution**: Automatic overflow to Cloud Storage with retrieval mechanism.

```typescript
// Enhanced transcript overflow handling
const storeOverflowMessages = async (transcriptId: string, messages: any[]): Promise<string> => {
  const bucket = admin.storage().bucket();
  const fileName = `transcripts/${transcriptId}/overflow-${Date.now()}.json`;
  const file = bucket.file(fileName);

  try {
    await file.save(JSON.stringify(messages), {
      metadata: {
        contentType: 'application/json',
        transcriptId,
        messageCount: messages.length,
        createdAt: new Date().toISOString()
      }
    });

    console.log(`Stored ${messages.length} overflow messages for transcript ${transcriptId}`);
    return `gs://${bucket.name}/${fileName}`;

  } catch (error) {
    console.error(`Failed to store overflow messages for transcript ${transcriptId}:`, error);
    throw error;
  }
};

// Retrieve complete transcript including overflow
const getCompleteTranscript = async (transcriptId: string) => {
  const transcriptDoc = await admin.firestore()
    .collection('transcripts')
    .doc(transcriptId)
    .get();

  if (!transcriptDoc.exists) {
    throw new Error('Transcript not found');
  }

  const transcriptData = transcriptDoc.data();
  let allMessages = transcriptData.messages || [];

  // If messages were truncated, retrieve overflow from Cloud Storage
  if (transcriptData.isMessagesTruncated && transcriptData.fullTranscriptUrl) {
    try {
      const bucket = admin.storage().bucket();
      const fileName = transcriptData.fullTranscriptUrl.replace(`gs://${bucket.name}/`, '');
      const file = bucket.file(fileName);

      const [contents] = await file.download();
      const overflowMessages = JSON.parse(contents.toString());

      // Combine overflow messages with current messages
      allMessages = [...overflowMessages, ...allMessages];

    } catch (error) {
      console.error(`Failed to retrieve overflow messages for transcript ${transcriptId}:`, error);
      // Continue with available messages
    }
  }

  return {
    ...transcriptData,
    messages: allMessages,
    totalMessageCount: allMessages.length
  };
};
```

#### Performance Monitoring

```typescript
// Monitor slow queries and performance issues
export const monitorPerformance = functions.https.onCall(async (data, context) => {
  const { operation, duration, collection, filters } = data;

  // Log slow queries (>2 seconds)
  if (duration > 2000) {
    await admin.firestore().collection('_monitoring').add({
      type: 'slow_query',
      operation,
      duration,
      collection,
      filters,
      userId: context.auth?.uid,
      timestamp: admin.firestore.FieldValue.serverTimestamp()
    });

    console.warn(`Slow query detected: ${operation} on ${collection} took ${duration}ms`);
  }

  // Monitor dashboard size
  if (collection === 'dashboards') {
    const dashboardDoc = await admin.firestore()
      .collection('dashboards')
      .doc(filters.companyId)
      .get();

    if (dashboardDoc.exists) {
      const docSize = JSON.stringify(dashboardDoc.data()).length;

      if (docSize > 50000) { // 50KB warning threshold
        await admin.firestore().collection('_monitoring').add({
          type: 'large_dashboard_document',
          companyId: filters.companyId,
          documentSize: docSize,
          timestamp: admin.firestore.FieldValue.serverTimestamp()
        });

        console.warn(`Large dashboard document detected for company ${filters.companyId}: ${docSize} bytes`);
      }
    }
  }
});
```
