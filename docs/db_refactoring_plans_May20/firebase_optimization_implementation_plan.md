# Firebase Optimization Implementation Plan - Multi-Environment Strategy

## Overview

This implementation plan outlines the complete migration strategy for Recruiva's Firebase database optimization, focusing on a **multi-environment approach** with separate Firebase projects for development, demo, and production environments. The plan prioritizes **zero-downtime migration** by implementing the new architecture alongside the existing system without data migration.

## Multi-Environment Strategy

### Environment Structure

- **Development Environment**: `recruiva-dev` Firebase project
- **Demo Environment**: `recruiva-demo` Firebase project
- **Production Environment**: `recruiva-prod` Firebase project (existing: `recruiva`)

### Deployment Strategy

- **Vercel**: Separate projects for each environment
- **Render**: Separate backend deployments for each environment
- **Firebase**: New projects with optimized schema implementation

## Current Implementation Analysis

### Database Architecture Overview

Recruiva currently uses **Firebase** as its primary database solution, specifically **Cloud Firestore**, a NoSQL document-oriented database. The application leverages:

- **Firestore**: For storing structured data in document collections
- **Firebase Authentication**: For user authentication and management
- **Firebase Storage**: For storing file assets (resumes, etc.)
- **Firebase Security Rules**: For access control
- **Cloud Functions**: For server-side processing and triggers

### Current Schema Structure

The database follows a deeply nested NoSQL structure with the following main collections and subcollections:

```
users
  └── roles
      ├── templates
      │   ├── questions
      │   └── evaluationCriteria
      ├── candidates
      ├── intakeTranscripts
      ├── interviews
      └── evaluations

roles (public)
  ├── templates
  │   ├── questions
  │   └── evaluationCriteria
  └── job_postings

candidates
  └── applications
      ├── evaluations
      └── interviews

applications (top-level)

evaluations (top-level)

public_interview_sessions

public_applications

public_evaluations
```

### Critical Issues

1. **Deep Nesting and Inefficient Queries**:

   - Multiple round trips for nested data
   - Client-side filtering instead of server-side queries
   - Lack of proper indexing for complex queries
2. **Document Size Limitations**:

   - Firestore has a 1MB limit per document, which is problematic for large transcripts
   - Nested collections approach has scaling limitations for roles with many candidates
3. **Data Redundancy**:

   - Same data stored in multiple collections (e.g., applications in user-specific paths and top-level)
   - Inconsistent update patterns causing data divergence
4. **Security Rule Complexity**:

   - Complex, nested security rules are difficult to maintain
   - Direct client access to Firestore bypasses backend validation in some cases
5. **Performance Issues**:

   - Loading entire documents when only specific fields are needed
   - Lack of pagination for list operations
   - No caching strategy for frequently accessed data
6. **Schema Inconsistency**:

   - No enforced schema leads to inconsistent document structures
   - Field normalization required in multiple places

## New Database Design Specification

The new database design follows the **Firebase Database Design Specification** document, implementing:

### Key Architectural Changes

1. **Flattened Collection Structure**: Eliminates deep nesting with top-level collections
2. **Multi-Tenant Support**: All collections include `companyId` for proper isolation
3. **Performance-Optimized Dashboards**: Size-limited dashboard collection with aggregated data
4. **Denormalized Display Fields**: Critical fields duplicated for efficient list views
5. **Cursor-Based Pagination**: Efficient pagination for large collections
6. **Compound Indexes**: Optimized for multi-tenant query patterns

### New Collection Structure

```
companies/
dashboards/           # Size-limited with 5 recent items + aggregates
users/
jobs/                 # Renamed from roles
templates/
questions/
criteria/
applications/         # Denormalized with essential display fields
candidates/
resumes/
resumeEvaluations/
interviewEvaluations/
interviews/
transcripts/          # Sub-collections for large message arrays
jobPostings/
```

## Optimization Objectives

The primary goal is to implement the new Firebase/Firestore architecture to:

1. **Implement flattened database structure** based on the new specification
2. **Enable multi-tenant support** with proper company isolation
3. **Optimize dashboard performance** with size-limited aggregated data
4. **Implement proper indexing** for all multi-tenant query patterns
5. **Establish consistent document schemas** with TypeScript/Pydantic validation
6. **Eliminate data redundancy** through single sources of truth
7. **Optimize for selective field loading** to minimize data transfer
8. **Improve security** by moving direct Firestore access behind API endpoints
9. **Implement caching** for frequently accessed data
10. **Enable cursor-based pagination** for all list operations

## Implementation Plan

The implementation plan is organized into 8 major phases, focusing on **zero-downtime migration** and **multi-environment setup**.

### IMPORTANT Considerations about the current setup

* The user wants to use the local environment with development config and deploy to dev environments from the local machine.
* The backend is ran using docker on the local machine for development
* The docker container has the hot_reload enabled for the backend/ directory
* The docker container has the port 8000 exposed
* During the implementation the current existing app running on the legacy prod named "recruiva" on firebase shall continue working without intruption"

### Phase 1: Multi-Environment Setup and Infrastructure

**Objective**: Set up separate Firebase projects and deployment environments for development, demo, and production.

#### Steps:

1.1. **Firebase Project Creation**

- [X] Create `recruiva-dev` Firebase project for development
- [X] Create `recruiva-demo` Firebase project for demo environment
- [X] Create `recruiva-prod` Firebase project for demo environment
- [X] Keep existing `recruiva` as production environment
- [X] Configure Firebase Authentication for each project
- [X] Set up Firebase Storage buckets for each environment

1.2. **Environment Configuration**

- [X] Create environment-specific configuration files. (Basic config files created but need to be compared with the legacy config for feature parity. Configuration files for front end we only created dev.ts)
- [X] Set up Firebase service account keys for each environment
- [X] Configure environment variables for backend and frontend
- [X] Implement environment detection and switching logic

1.3. **Deployment Pipeline Setup**

- [X] Create separate Vercel projects for each environment (only created `recruiva-dev` on vercel in adition to existing legacy prod which is `recruiva-frontend`)
- [X] Set up Render backend deployments for each environment (only created `recruiva-dev` on vercel in adition to existing legacy prod which is `recruiva-backend`)
- [X] Configure CI/CD pipelines for automated deployments
- [X] Implement environment-specific build configurations
- [X] Create neccesary docker container for local tests with hotReload enabled to `backend/` and port 8000 exposed, this should use `.env.loca` and the `dev` config files.

1.4. **Security and Access Control**

- [X] Configure Firebase security rules for each environment
- [X] Set up IAM roles and permissions for each project
- [X] Implement environment-specific API keys and secrets
- [X] Configure CORS and domain restrictions

#### Acceptance Criteria:

- [X] Three separate Firebase projects operational
- [X] Environment-specific deployments on Vercel and Render
- [X] Automated CI/CD pipelines for each environment
- [X] Proper security isolation between environments

#### Testing:

- [X] Verify environment isolation and configuration
- [X] Test deployment pipelines for each environment
- [X] Validate security rules and access controls
- [X] Confirm environment-specific data separation

**Estimated Duration**: 1 day

### Phase 2: Multi-Tenant Authentication System Implementation

**Objective**: Refactor the current authentication system (no impact on the firebase `recruiva` project, these are on the `recruiva-dev` project)to support multi-tenant company-based user management.

#### Steps:

2.1. **Company Management System (follow the company data schema in the `firebase_database_design_specification.md`)**

INSTRUCTIONS:

- Make sure the backend is well structured and we have clearly defined vertical slices with self contained services for example the company service
- DO NOT COMPLECATE things
- Implement in the most efficient way following the design specification

- [ ] Create company registration and management APIs
- [ ] Implement company domain verification system
- [ ] Add company admin role and permissions
- [ ] Create company settings and configuration management

2.2. **Enhanced User Registration Flow**

- [ ] Implement domain-based company detection during signup
- [ ] Add company invitation system for new users
- [ ] Create individual account to company account conversion
- [ ] Implement email domain validation and verification

2.3. **Authentication Service Refactoring**

- [ ] Update FirestoreUser interface to include companyId and enhanced fields
- [ ] Implement company-based user lookup and validation
- [ ] Add custom claims for companyId and enhanced role management
- [ ] Create company admin authentication flows

2.4. **Frontend Authentication Updates**

- [ ] Update auth context to handle company information
- [ ] Implement company selection during registration
- [ ] Add company admin dashboard and user management
- [ ] Create company invitation acceptance flows

#### Acceptance Criteria:

- [ ] Company registration and management system operational
- [ ] Domain-based user assignment to companies working
- [ ] Individual accounts can create single-user companies
- [ ] Enhanced user roles with company-based permissions

#### Testing:

- [ ] Company registration and domain verification tests
- [ ] User assignment to companies validation
- [ ] Individual to company account conversion tests
- [ ] Multi-tenant authentication flow tests

**Estimated Duration**: 1 day

### Phase 3: Database Schema Design and Standardization

**Objective**: Implement the new database schema based on the Firebase Database Design Specification.

#### Steps:

3.1. **Schema Implementation**

- [ ] Implement all collections according to the specification
- [ ] Create Firestore indexes as defined in the specification with multi-tenant support
- [ ] Set up multi-tenant security rules for the new schema
- [ ] Configure Cloud Functions for dashboard updates and company management

3.2. **TypeScript Interface Definition**

- [ ] Define TypeScript interfaces for all collections including company fields
- [ ] Create Data Transfer Objects (DTOs) for API responses with multi-tenant data
- [ ] Implement type guards and validation utilities
- [ ] Document field types, constraints, and multi-tenant relationships

3.3. **Backend Model Implementation**

- [ ] Create Pydantic models for backend validation with company support
- [ ] Implement repository pattern for data access with multi-tenant filtering
- [ ] Add field selection and pagination support
- [ ] Create data transformation utilities for company-based data

3.4. **Validation and Testing**

- [ ] Create schema validation utilities for both frontend and backend
- [ ] Implement validation middleware for API endpoints with company checks
- [ ] Add runtime type checking for critical data operations
- [ ] Test schema with sample multi-tenant data

#### Acceptance Criteria:

- [ ] Complete schema implementation according to specification
- [ ] TypeScript interfaces and Pydantic models implemented with multi-tenant support
- [ ] Schema validation utilities implemented and tested
- [ ] All indexes and multi-tenant security rules configured

#### Testing:

- [ ] Unit tests for schema validation utilities
- [ ] Validation of sample documents against schemas
- [ ] Multi-tenant data isolation testing
- [ ] Security rule validation tests

**Estimated Duration**: 2 weeks

### Phase 4: Data Access Layer Refactoring

**Objective**: Create a unified data access layer that implements the new schema with repository pattern and optimized queries.

#### Steps:

4.1. **Repository Pattern Implementation**

- Create abstract repository interfaces for each domain entity
- Implement Firestore-specific repositories for new schema
- Add transaction handling for multi-document operations
- Implement field selection for partial document retrieval
- Add multi-tenant query support with companyId filtering

4.2. **Query Optimization and Pagination**

- Implement cursor-based pagination for all collections
- Create optimized query methods for dashboard aggregations
- Add compound index support for multi-tenant queries
- Implement efficient filtering and sorting patterns

4.3. **Data Transformation Layer**

- Create data transformers for new schema format
- Implement denormalization utilities for display fields
- Add consistent error handling for all database operations
- Create utilities for dashboard data aggregation

4.4. **Caching Implementation**

- Implement Redis caching for frequently accessed data
- Add cache invalidation strategies for data updates
- Create cache warming for dashboard aggregations
- Implement cache monitoring and metrics

#### Acceptance Criteria:

- Repository implementations for all collections in new schema
- Cursor-based pagination for all list operations
- Field selection support for all get operations
- Multi-tenant query support with proper isolation
- Caching layer with invalidation strategies

#### Testing:

- Unit tests for each repository method
- Performance tests for pagination and filtering
- Multi-tenant isolation tests
- Cache hit/miss rate validation
- Transaction consistency tests

**Estimated Duration**: 3 weeks

### Phase 5: Backend API Implementation

**Objective**: Implement new API endpoints for the optimized schema while maintaining backward compatibility.

#### Steps:

5.1. **New API Endpoint Development**

- Create new API endpoints for all collections in new schema
- Implement multi-tenant filtering for all endpoints
- Add field selection support for GET operations
- Implement cursor-based pagination for list endpoints
- Add company management and user invitation endpoints

5.2. **Service Layer Implementation**

- Create new service classes using the repository pattern
- Implement dashboard aggregation services
- Add comprehensive validation for all service methods
- Implement proper error handling with detailed error messages
- Create company-based user management services

5.3. **Backward Compatibility Layer**

- Create adapter services to maintain existing API compatibility
- Implement data transformation between old and new formats
- Add feature flags for gradual migration
- Ensure existing frontend continues to work

5.4. **Security and Validation**

- Update Firestore security rules for new schema with multi-tenant support
- Implement rate limiting for public endpoints
- Add request validation middleware with company checks
- Create role-based access control with company isolation
- Implement company admin permissions and validation

#### Acceptance Criteria:

- New API endpoints implemented for all collections
- Multi-tenant support with proper company isolation
- Company management and user invitation APIs operational
- Backward compatibility maintained for existing APIs
- Comprehensive validation and error handling
- Security rules enforcing proper access control

#### Testing:

- API endpoint tests for all CRUD operations
- Multi-tenant isolation validation
- Company management functionality tests
- Backward compatibility tests
- Security rule validation tests
- Performance tests for new endpoints

**Estimated Duration**: 3 weeks

### Phase 6: Frontend Service Layer Refactoring

**Objective**: Update frontend services to use new API endpoints and implement optimized data fetching patterns.

#### Steps:

6.1. **Service Layer Updates**

- Refactor frontend service classes to use new API endpoints
- Implement field selection for data requests
- Add cursor-based pagination support for list components
- Create optimized data fetching strategies
- Add company context to all API calls

6.2. **Type System Updates**

- Update TypeScript interfaces to match new schema with company fields
- Implement type guards for runtime validation
- Create DTOs for API responses with multi-tenant data
- Add proper error type definitions
- Update FirestoreUser interface with company information

6.3. **State Management Optimization**

- Implement efficient caching in frontend state management
- Add optimistic updates for better UX
- Create data normalization strategies with company context
- Implement proper loading and error states
- Add company-based data isolation in state

6.4. **Component Integration**

- Update components to use new service interfaces
- Implement progressive loading for large datasets
- Add skeleton UI for loading states
- Create reusable pagination components
- Update authentication components for multi-tenant support

#### Acceptance Criteria:

- Frontend services updated to use new API patterns with multi-tenant support
- TypeScript interfaces aligned with new schema including company fields
- Efficient state management with caching and company context
- Components optimized for new data patterns
- Authentication components support multi-tenant flows

#### Testing:

- Unit tests for updated service classes
- Integration tests for component updates
- Multi-tenant data isolation tests
- Performance tests for data fetching
- Type safety validation tests

**Estimated Duration**: 2 weeks

### Phase 7: Development Environment Testing and Validation

**Objective**: Thoroughly test the new architecture in the development environment before proceeding to demo and production.

#### Steps:

7.1. **Comprehensive Testing**

- Perform end-to-end testing of all user flows including multi-tenant scenarios
- Validate multi-tenant isolation and security
- Test performance improvements and optimizations
- Verify dashboard aggregations and real-time updates
- Test company registration and user invitation flows

7.2. **Data Population and Validation**

- Create comprehensive test data for all scenarios including multi-company setups
- Validate data integrity and consistency across companies
- Test edge cases and error conditions
- Verify proper handling of large datasets
- Test company admin and user management features

7.3. **Performance Benchmarking**

- Measure query performance improvements
- Test pagination and filtering efficiency with multi-tenant data
- Validate cache hit rates and effectiveness
- Compare performance against current production
- Test dashboard performance with multiple companies

7.4. **Security and Compliance Testing**

- Validate security rules and access controls for multi-tenant setup
- Test multi-tenant data isolation thoroughly
- Verify proper authentication and authorization with company context
- Conduct security penetration testing for company boundaries
- Test company admin permissions and restrictions

#### Acceptance Criteria:

- All user flows working correctly in development including multi-tenant scenarios
- Performance improvements validated and documented
- Security and multi-tenant isolation confirmed
- Company management and user invitation features working
- Comprehensive test coverage achieved

#### Testing:

- Automated test suite execution
- Manual testing of critical user paths
- Multi-tenant isolation validation
- Performance benchmarking reports
- Security audit and penetration testing

**Estimated Duration**: 2 weeks

### Phase 8: Demo Environment Setup and Production Preparation

**Objective**: Set up demo environment and prepare for production migration without data transfer.

#### Steps:

8.1. **Demo Environment Deployment**

- Deploy new architecture to demo environment
- Configure demo-specific settings and data
- Set up monitoring and logging for demo environment
- Create demo user accounts and sample multi-tenant data
- Configure company registration and user invitation flows

8.2. **Production Migration Strategy**

- Plan production deployment without data migration
- Create feature flags for gradual rollout
- Implement monitoring and alerting for production
- Prepare rollback procedures and contingency plans
- Plan company onboarding and migration communication

8.3. **Documentation and Training**

- Create comprehensive documentation for new architecture
- Document API changes and migration guides
- Prepare training materials for development team
- Create operational runbooks for production support
- Document multi-tenant setup and company management procedures

8.4. **Final Validation**

- Conduct final security review and penetration testing
- Perform load testing with production-like data volumes
- Validate disaster recovery and backup procedures
- Confirm monitoring and alerting systems
- Test multi-tenant isolation under load

#### Acceptance Criteria:

- Demo environment fully operational with new architecture
- Multi-tenant features working in demo environment
- Production deployment plan documented and approved
- Comprehensive documentation and training materials ready
- Security and performance validation completed

#### Testing:

- Demo environment functionality testing
- Multi-tenant isolation testing in demo
- Load testing with production-scale data
- Security and compliance validation
- Disaster recovery testing

**Estimated Duration**: 2 weeks

### Phase 9: Production Deployment and Legacy Cleanup

**Objective**: Deploy new architecture to production and safely remove old implementations.

#### Steps:

9.1. **Production Deployment**

- Deploy new architecture to production environment
- Implement gradual rollout with feature flags
- Monitor system performance and user experience
- Address any production-specific issues
- Enable company registration and user invitation features

9.2. **Legacy System Deprecation**

- Gradually disable old API endpoints
- Remove direct Firestore access from frontend
- Archive old collections and data structures
- Update documentation to reflect new architecture
- Communicate migration to existing users

9.3. **Performance Monitoring and Optimization**

- Monitor system performance in production
- Optimize based on real-world usage patterns
- Fine-tune caching and indexing strategies
- Implement additional optimizations as needed
- Monitor multi-tenant performance and isolation

9.4. **Final Cleanup and Documentation**

- Remove deprecated code and old implementations
- Update all documentation and API references
- Conduct final security audit
- Create post-migration performance reports
- Document company onboarding procedures

#### Acceptance Criteria:

- New architecture successfully deployed to production
- Multi-tenant features operational in production
- Legacy systems safely deprecated and removed
- Performance improvements validated in production
- Company onboarding and user management working
- Complete documentation and cleanup achieved

#### Testing:

- Production deployment validation
- Multi-tenant isolation validation in production
- Performance monitoring and reporting
- User acceptance testing in production
- Final security and compliance audit

**Estimated Duration**: 2 weeks

## Repository Organization Strategy

### Directory Structure for Multi-Environment Support

```
/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   │   ├── v1/          # Legacy API endpoints
│   │   │   └── v2/          # New optimized API endpoints
│   │   ├── services/
│   │   │   ├── legacy/      # Legacy service implementations
│   │   │   └── optimized/   # New optimized services
│   │   ├── repositories/    # New repository pattern implementations
│   │   ├── models/
│   │   │   ├── legacy/      # Legacy Pydantic models
│   │   │   └── optimized/   # New schema models
│   │   └── utils/
│   ├── config/
│   │   ├── dev.py          # Development environment config
│   │   ├── demo.py         # Demo environment config
│   │   └── prod.py         # Production environment config
│   └── migrations/         # Data transformation scripts
├── frontend/
│   ├── src/
│   │   ├── services/
│   │   │   ├── legacy/     # Legacy service implementations
│   │   │   └── optimized/  # New optimized services
│   │   ├── types/
│   │   │   ├── legacy/     # Legacy TypeScript interfaces
│   │   │   └── optimized/  # New schema interfaces
│   │   ├── components/
│   │   │   ├── legacy/     # Components using legacy APIs
│   │   │   └── optimized/  # Components using new APIs
│   │   └── config/
│   │       ├── dev.ts      # Development environment config
│   │       ├── demo.ts     # Demo environment config
│   │       └── prod.ts     # Production environment config
├── firebase/
│   ├── dev/                # Development Firebase configuration
│   ├── demo/               # Demo Firebase configuration
│   └── prod/               # Production Firebase configuration
└── docs/
    ├── migration/          # Migration documentation
    ├── api/               # API documentation
    └── architecture/      # Architecture documentation
```

### File Cleanup Strategy

1. **Phase 1-6**: Keep both legacy and optimized implementations
2. **Phase 7**: Begin deprecating legacy implementations in demo
3. **Phase 8**: Remove legacy implementations after successful production deployment

### Environment-Specific Configuration Management

```typescript
// frontend/src/config/environment.ts
export const getEnvironmentConfig = () => {
  const env = process.env.NODE_ENV || 'development';

  switch (env) {
    case 'development':
      return require('./dev').default;
    case 'demo':
      return require('./demo').default;
    case 'production':
      return require('./prod').default;
    default:
      return require('./dev').default;
  }
};
```

## Detailed Technical Implementation

### Database Structure Transformation

#### Current Nested Structure:

```
users/<userId>/roles/<roleId>/templates/<templateId>/questions/<questionId>
users/<userId>/roles/<roleId>/candidates/<candidateId>/applications/<applicationId>
public_interview_sessions/<sessionId>
public_applications/<applicationId>
```

#### New Flattened Structure with Multi-Tenancy:

```
companies/<companyId>
dashboards/<companyId>                    # Size-limited dashboard data
users/<userId> (with companyId field)
jobs/<jobId> (with userId and companyId fields)
templates/<templateId> (with jobId and companyId fields)
questions/<questionId> (with templateId and companyId fields)
applications/<applicationId> (with denormalized display fields)
candidates/<candidateId>
interviews/<interviewId>
transcripts/<transcriptId>
  └── messages/<messageId>               # Sub-collection for large arrays
interviewEvaluations/<evaluationId>
resumeEvaluations/<evaluationId>
```

#### Key Improvements:

1. **Multi-Tenant Support**: All collections include `companyId` for proper isolation
2. **Denormalized Display Fields**: Applications include `candidateName`, `jobTitle` for efficient list views
3. **Size-Limited Dashboards**: Dashboard collection with max 5 recent items + aggregates
4. **Sub-Collections for Large Arrays**: Transcript messages stored as sub-collection
5. **Compound Indexes**: Optimized for multi-tenant query patterns

### Field Selection Implementation

#### Current Pattern:

```typescript
// Frontend - Gets entire nested document
const role = await rolesApi.getRole(roleId);

// Backend - Nested collection access
async def get_role(self, user_id: str, role_id: str) -> dict:
    doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id)
    doc = doc_ref.get()
    return doc.to_dict()  # Returns the entire document
```

#### New Optimized Pattern:

```typescript
// Frontend - Field selection and multi-tenant support
const jobSummary = await jobsApi.getJob(jobId, {
  fields: ['title', 'status', 'summary', 'companyId'],
  companyId: currentUser.companyId
});

// Backend - Flattened collection with field selection
async def get_job(self, job_id: str, company_id: str, fields: List[str] = None) -> dict:
    doc_ref = self.db.collection('jobs').document(job_id)

    # Multi-tenant security check
    if fields:
        doc = doc_ref.get(field_paths=fields + ['companyId'])
    else:
        doc = doc_ref.get()

    job_data = doc.to_dict()
    if job_data.get('companyId') != company_id:
        raise PermissionError("Access denied")

    return job_data
```

### Pagination Implementation

#### Current Pattern:

```typescript
// Frontend - Gets all roles at once (inefficient)
const roles = await rolesApi.listRoles();

// Backend - Nested collection access without pagination
async def get_roles(self, user_id: str) -> list:
    roles = []
    docs = self.db.collection('users').document(user_id).collection('roles').stream()
    for doc in docs:
        roles.append(doc.to_dict())
    return roles
```

#### New Cursor-Based Pagination Pattern:

```typescript
// Frontend - Cursor-based pagination with multi-tenant support
const jobsPage = await jobsApi.listJobs({
  companyId: currentUser.companyId,
  pageSize: 10,
  cursor: lastJobCursor,
  fields: ['id', 'title', 'status', 'updatedAt']
});

// Backend - Optimized multi-tenant pagination
async def get_jobs(
    self,
    company_id: str,
    page_size: int = 10,
    cursor: str = None,
    fields: List[str] = None
) -> dict:
    # Base query with multi-tenant filtering
    query = self.db.collection('jobs').where('companyId', '==', company_id)

    # Add cursor for pagination
    if cursor:
        cursor_doc = self.db.collection('jobs').document(cursor).get()
        query = query.start_after(cursor_doc)

    # Order by updatedAt for consistent pagination
    query = query.order_by('updatedAt', direction=firestore.Query.DESCENDING)
    query = query.limit(page_size + 1)  # Get one extra to check for more pages

    docs = list(query.stream())

    # Extract data with field selection
    jobs = []
    for doc in docs[:page_size]:
        job_data = doc.to_dict()
        if fields:
            job_data = {field: job_data.get(field) for field in fields}
        job_data['id'] = doc.id
        jobs.append(job_data)

    # Determine if there are more pages
    has_more = len(docs) > page_size
    next_cursor = docs[page_size - 1].id if jobs and has_more else None

    return {
        "data": jobs,
        "pagination": {
            "pageSize": page_size,
            "hasMore": has_more,
            "nextCursor": next_cursor
        }
    }
```

### Caching Implementation

#### Backend Caching:

```python
import redis
import json
from functools import wraps

# Initialize Redis client
redis_client = redis.Redis(host='localhost', port=6379, db=0)
CACHE_TTL = 300  # 5 minutes

def cache_result(prefix, ttl=CACHE_TTL):
    """Decorator to cache function results in Redis."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            key_parts = [prefix]
            for arg in args[1:]:  # Skip self
                key_parts.append(str(arg))
            for k, v in sorted(kwargs.items()):
                key_parts.append(f"{k}:{v}")
            cache_key = ":".join(key_parts)

            # Try to get from cache
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)

            # If not in cache, call the function
            result = await func(*args, **kwargs)

            # Cache the result
            redis_client.setex(cache_key, ttl, json.dumps(result))
            return result
        return wrapper
    return decorator

class RolesService:
    @cache_result("role")
    async def get_role(self, role_id: str, user_id: str) -> dict:
        # Implementation...
        pass
```

#### Frontend Caching:

```typescript
class RolesService {
  private roleCache: Map<string, Role> = new Map();
  private cacheTTL: number = 5 * 60 * 1000;  // 5 minutes

  async getRole(roleId: string, options?: { fields?: string[] }): Promise<Role | null> {
    const cacheKey = this.getCacheKey(roleId, options);

    // Check cache first
    const cached = this.roleCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      return cached.data;
    }

    // If not in cache or expired, fetch from API
    const role = await rolesApi.getRole(roleId, options);

    // Update cache
    if (role) {
      this.roleCache.set(cacheKey, {
        data: role,
        timestamp: Date.now()
      });
    }

    return role;
  }

  private getCacheKey(roleId: string, options?: any): string {
    if (!options) return roleId;
    return `${roleId}:${JSON.stringify(options)}`;
  }

  invalidateCache(roleId: string): void {
    // Remove all cache entries related to this role
    this.roleCache.forEach((value, key) => {
      if (key.startsWith(roleId)) {
        this.roleCache.delete(key);
      }
    });
  }
}
```

## Migration Strategy

### Preparation Phase:

1. **Create a snapshot of the current database** for backup and verification
2. **Set up the new flattened collections** without removing existing nested collections
3. **Implement dual-write functionality** for critical operations during transition

### Migration Process:

1. **Read from original collections** and transform to new schema
2. **Write to new collections** with data validation
3. **Verify data integrity** between old and new collections
4. **Update security rules** to restrict access to old collections
5. **Switch read operations** to use new collections
6. **Monitor for issues** and implement rollback if needed

### Rollback Strategy:

1. **Maintain old collections** until migration is verified
2. **Create rollback triggers** for automatic reversion if errors exceed threshold
3. **Keep dual read capability** for critical paths

## Indexing Strategy

### Core Indexes:

1. **User Roles Index**: `roles` collection indexed by `userId` for efficient user role queries
2. **Role Templates Index**: `templates` collection indexed by `roleId` for efficient role template queries
3. **Application Status Index**: `applications` collection indexed by `status` and `roleId` for filtering applications by status
4. **Evaluation Score Index**: `evaluations` collection indexed by `score` (descending) and `roleId` for ranking evaluations

### Compound Indexes:

1. **Role Search Index**: `roles` collection indexed by `status`, `jobType`, and `updatedAt` for filtering and sorting roles
2. **Application Timeline Index**: `applications` collection indexed by `roleId`, `status`, and `submittedAt` for role-specific application timelines
3. **Template Usage Index**: `templates` collection indexed by `usage_count` (descending) and `createdAt` for finding popular templates

## Performance Monitoring and Testing

### Key Metrics:

1. **Query Execution Time**: Before and after optimization for key operations
2. **Document Size Reduction**: Comparison of data transfer size with field selection
3. **Cache Hit Rate**: Percentage of requests served from cache
4. **API Response Time**: End-to-end response time for common operations
5. **Firestore Read/Write Operations**: Count of operations before and after optimization

### Testing Methodology:

1. **Load Testing**: Simulate multiple users performing common operations
2. **Scenario Testing**: Test complete user flows with realistic data volumes
3. **A/B Testing**: Compare performance between old and new implementations
4. **Long-term Performance Monitoring**: Track metrics over time to identify degradation

## Overall Acceptance Criteria

For the entire implementation plan to be considered successful, the following criteria must be met:

1. **Multi-Environment Success**:

   - Three separate Firebase projects operational (dev, demo, prod)
   - Environment-specific deployments on Vercel and Render
   - Proper isolation and security between environments
   - Automated CI/CD pipelines for each environment
2. **Performance Improvement**:

   - 70% reduction in average query time for common operations
   - 50% reduction in data transfer size with field selection
   - 80% cache hit rate for frequently accessed data
   - Dashboard loading under 2 seconds with aggregated data
3. **Architecture Enhancement**:

   - Complete implementation of new flattened schema
   - Multi-tenant support with proper company isolation
   - Cursor-based pagination for all list operations
   - Size-limited dashboard collections preventing 1MB limits
4. **Security and Compliance**:

   - All direct Firestore access replaced with API endpoints
   - Multi-tenant security rules enforcing company isolation
   - Rate limiting implemented for public endpoints
   - Comprehensive audit trail for all data access
5. **User Experience**:

   - Improved UI responsiveness for data-heavy pages
   - Faster initial loading times for the application
   - Smooth pagination for list views
   - Real-time dashboard updates with aggregated data
6. **Developer Experience**:

   - Clear documentation for the new data access patterns
   - Simplified service implementations with the repository pattern
   - Consistent error handling across the application
   - TypeScript interfaces aligned with new schema
7. **Zero-Downtime Deployment**:

   - Successful production deployment without data migration
   - No service interruption during transition
   - Rollback capability tested and verified
   - User communication and support documentation complete

### Production Deployment Strategy

1. **Feature Flag Rollout**: Gradually enable new features for user segments
2. **Monitoring**: Comprehensive monitoring of new system performance
3. **Rollback Capability**: Ability to quickly revert to old system if needed
4. **User Communication**: Clear communication about the fresh start approach
