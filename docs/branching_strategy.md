# Branching Strategy for Multi-Environment Setup

## Branches

- `main`: Current production environment (connected to existing `recruiva` Firebase)
- `dev`: Development environment (connected to `recruiva-dev` Firebase)
- `demo`: Demo environment (connected to `recruiva-demo` Firebase)
- `prod`: New production environment (connected to `recruiva-prod` Firebase)

## Workflow

1. **Feature Development**:
   - Create feature branches from `dev`
   - Name format: `feature/feature-name`
   - <PERSON>elo<PERSON> and test locally
   - Create PR to merge back into `dev`

2. **Bug Fixes**:
   - Create bug fix branches from the affected environment branch
   - Name format: `fix/bug-description`
   - Fix and test locally
   - Create PR to merge back into the source branch

3. **Environment Promotion**:
   - Development to Demo: Create PR from `develop` to `demo`
   - Demo to New Production: Create PR from `demo` to `prod`

4. **Production Hotfixes**:
   - For current production: Create hotfix branch from `main`
   - Name format: `hotfix/issue-description`
   - Fix and test locally
   - Create PR to merge back into `main`
   - Cherry-pick or create separate PRs to apply the same fix to other environment branches

## Release Process

1. Merge changes to `dev` for development testing
2. Promote to `demo` for stakeholder review
3. Promote to `prod` for final validation
4. When ready to switch to new production infrastructure, merge `prod` into `main`

## Notes

- The `main` branch will continue to use the existing `recruiva` Firebase project until the migration is complete
- After successful migration, `main` will be updated to use the `recruiva-prod` Firebase project
- The `prod` branch will be retired after successful migration