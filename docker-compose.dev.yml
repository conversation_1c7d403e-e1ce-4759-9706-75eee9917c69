services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/__pycache__
      - /app/app/__pycache__
    environment:
      - ENVIRONMENT=development
      - PORT=8000
      - PYTHONUNBUFFERED=1
    env_file:
      - ./backend/.env.local
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - recruiva-dev

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_ENVIRONMENT=development
    env_file:
      - ./frontend/.env.development
    command: npm run dev
    networks:
      - recruiva-dev
    depends_on:
      - backend

networks:
  recruiva-dev:
    driver: bridge
