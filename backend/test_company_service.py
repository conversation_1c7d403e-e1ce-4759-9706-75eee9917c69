#!/usr/bin/env python3
"""
Test script for Company Management System - Phase 2 Step 2.1

This script tests the basic functionality of the CompanyService
to ensure it follows the Firebase database design specification.
"""

import asyncio
import sys
import os
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.company_service import CompanyService
from app.api.v1.models import CompanyCreate, CompanyUpdate

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_company_service():
    """Test the CompanyService implementation."""
    
    print("🏢 Testing Company Management System - Phase 2 Step 2.1")
    print("=" * 60)
    
    try:
        # Initialize service
        company_service = CompanyService()
        print("✅ CompanyService initialized successfully")
        
        # Test data following the exact schema
        test_company_data = {
            "name": "Test Company Inc",
            "domain": "testcompany.com",
            "industry": "Technology",
            "size": "50-100",
            "address": "123 Test Street, Test City, TC 12345",
            "phone": "******-123-4567",
            "email": "<EMAIL>",
            "recruivaEmail": "<EMAIL>",
            "billingAccount": "BILL-12345",
            "settings": {
                "notifications": True,
                "theme": "light",
                "timezone": "UTC"
            },
            "status": "active"
        }
        
        print("\n📝 Test Data Prepared:")
        print(f"   Company Name: {test_company_data['name']}")
        print(f"   Domain: {test_company_data['domain']}")
        print(f"   Email: {test_company_data['email']}")
        print(f"   Recruiva Email: {test_company_data['recruivaEmail']}")
        
        # Test 1: Create Company
        print("\n🔧 Test 1: Creating Company...")
        try:
            created_company = await company_service.create_company(test_company_data)
            company_id = created_company["id"]
            print(f"✅ Company created successfully with ID: {company_id}")
            
            # Verify all required fields are present
            required_fields = ["id", "name", "address", "phone", "email", "status", "createdAt", "updatedAt"]
            for field in required_fields:
                if field not in created_company:
                    print(f"❌ Missing required field: {field}")
                    return False
            print("✅ All required fields present in created company")
            
        except Exception as e:
            print(f"❌ Company creation failed: {str(e)}")
            return False
        
        # Test 2: Get Company
        print("\n🔍 Test 2: Retrieving Company...")
        try:
            retrieved_company = await company_service.get_company(company_id)
            if retrieved_company and retrieved_company["id"] == company_id:
                print("✅ Company retrieved successfully")
                print(f"   Name: {retrieved_company['name']}")
                print(f"   Status: {retrieved_company['status']}")
            else:
                print("❌ Company retrieval failed")
                return False
        except Exception as e:
            print(f"❌ Company retrieval failed: {str(e)}")
            return False
        
        # Test 3: Update Company
        print("\n📝 Test 3: Updating Company...")
        try:
            update_data = {
                "industry": "Software Development",
                "size": "100-200",
                "settings": {
                    "notifications": False,
                    "theme": "dark",
                    "timezone": "PST"
                }
            }
            updated_company = await company_service.update_company(company_id, update_data)
            if updated_company["industry"] == "Software Development":
                print("✅ Company updated successfully")
                print(f"   New Industry: {updated_company['industry']}")
                print(f"   New Size: {updated_company['size']}")
            else:
                print("❌ Company update failed")
                return False
        except Exception as e:
            print(f"❌ Company update failed: {str(e)}")
            return False
        
        # Test 4: List Companies
        print("\n📋 Test 4: Listing Companies...")
        try:
            companies = await company_service.list_companies(limit=10)
            if companies and len(companies) > 0:
                print(f"✅ Found {len(companies)} companies")
                for company in companies[:3]:  # Show first 3
                    print(f"   - {company['name']} ({company['status']})")
            else:
                print("❌ No companies found")
                return False
        except Exception as e:
            print(f"❌ Company listing failed: {str(e)}")
            return False
        
        # Test 5: Domain Verification
        print("\n🔐 Test 5: Domain Verification...")
        try:
            verification_result = await company_service.verify_company_domain(
                company_id, 
                "verified-testcompany.com"
            )
            if verification_result["verified"]:
                print("✅ Domain verification successful")
                print(f"   Verified Domain: {verification_result['domain']}")
            else:
                print("❌ Domain verification failed")
                return False
        except Exception as e:
            print(f"❌ Domain verification failed: {str(e)}")
            return False
        
        # Test 6: Settings Management
        print("\n⚙️ Test 6: Settings Management...")
        try:
            # Get current settings
            current_settings = await company_service.get_company_settings(company_id)
            print(f"✅ Current settings retrieved: {len(current_settings)} items")
            
            # Update settings
            new_settings = {"newFeature": True, "apiVersion": "v2"}
            updated_company = await company_service.update_company_settings(company_id, new_settings)
            
            if "newFeature" in updated_company["settings"]:
                print("✅ Settings updated successfully")
            else:
                print("❌ Settings update failed")
                return False
        except Exception as e:
            print(f"❌ Settings management failed: {str(e)}")
            return False
        
        # Test 7: Cleanup - Delete Company
        print("\n🗑️ Test 7: Cleaning up - Deleting Company...")
        try:
            deletion_result = await company_service.delete_company(company_id)
            if deletion_result:
                print("✅ Company deleted successfully")
            else:
                print("❌ Company deletion failed")
                return False
        except Exception as e:
            print(f"❌ Company deletion failed: {str(e)}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! Company Management System is working correctly.")
        print("✅ Phase 2 Step 2.1 implementation is successful!")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    success = await test_company_service()
    if not success:
        sys.exit(1)
    print("\n🚀 Ready for next phase of implementation!")

if __name__ == "__main__":
    asyncio.run(main())
