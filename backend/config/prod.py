# Production environment configuration
FIREBASE_PROJECT_ID = "recruiva"
FIREBASE_STORAGE_BUCKET = "recruiva.appspot.com"
API_URL = "https://recruiva-backend.onrender.com"
FRONTEND_URL = "https://www.recruiva.ai"
DEBUG = False

# Firebase Configuration (Production project keys - using existing production values)
FIREBASE_API_KEY = "AIzaSyBJGJGJGJGJGJGJGJGJGJGJGJGJGJGJGJG"  # To be updated with actual production key
FIREBASE_AUTH_DOMAIN = "recruiva.firebaseapp.com"
FIREBASE_MESSAGING_SENDER_ID = "PROD_SENDER_ID_TO_BE_CONFIGURED"
FIREBASE_APP_ID = "PROD_APP_ID_TO_BE_CONFIGURED"
FIREBASE_MEASUREMENT_ID = "PROD_MEASUREMENT_ID_TO_BE_CONFIGURED"

# Environment-specific settings
ENVIRONMENT = "production"
EMAIL_LISTENER_ENABLED = True  # Enabled for production
ENABLE_EMAIL_MONITORING = True  # Enabled for production monitoring