# Demo environment configuration
FIREBASE_PROJECT_ID = "recruiva-demo"
FIREBASE_STORAGE_BUCKET = "recruiva-demo.appspot.com"
API_URL = "https://recruiva-demo.onrender.com"
FRONTEND_URL = "https://recruiva-demo.vercel.app"
DEBUG = False

# Firebase Configuration (Demo project keys - to be updated with actual values)
FIREBASE_API_KEY = "DEMO_API_KEY_TO_BE_CONFIGURED"
FIREBASE_AUTH_DOMAIN = "recruiva-demo.firebaseapp.com"
FIREBASE_MESSAGING_SENDER_ID = "DEMO_SENDER_ID_TO_BE_CONFIGURED"
FIREBASE_APP_ID = "DEMO_APP_ID_TO_BE_CONFIGURED"
FIREBASE_MEASUREMENT_ID = "DEMO_MEASUREMENT_ID_TO_BE_CONFIGURED"

# Environment-specific settings
ENVIRONMENT = "demo"
EMAIL_LISTENER_ENABLED = False  # Disabled for demo
ENABLE_EMAIL_MONITORING = True  # Enabled for demo monitoring