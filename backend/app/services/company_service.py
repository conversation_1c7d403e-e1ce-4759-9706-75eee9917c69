# File: backend/app/services/company_service.py

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from firebase_admin import firestore
from ..core.config import settings
from .firebase_service import FirebaseService
from fastapi import HTTPException
from google.cloud.firestore_v1.base_query import FieldFilter
import uuid

# Set up logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class CompanyService:
    """Service for managing company operations following the Firebase database design specification."""

    def __init__(self):
        self.firebase = FirebaseService()
        self.db = firestore.client()

    async def create_company(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new company following the exact schema from firebase_database_design_specification.md.

        Args:
            company_data: Dictionary containing company information

        Returns:
            Dictionary containing the created company data with ID

        Raises:
            HTTPException: If validation fails or company creation fails
        """
        try:
            logger.debug(f"Creating company with data: {company_data}")

            # Validate required fields according to the specification
            required_fields = ["name", "address", "phone", "email"]
            for field in required_fields:
                if not company_data.get(field):
                    raise HTTPException(
                        status_code=400,
                        detail=f"Missing required field: {field}"
                    )

            # Validate email format
            email = company_data["email"]
            if "@" not in email:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid email format"
                )

            # Check if company name already exists
            existing_company = await self._get_company_by_name(company_data["name"])
            if existing_company:
                raise HTTPException(
                    status_code=400,
                    detail="Company name already exists"
                )

            # Check if email already exists
            existing_email_company = await self._get_company_by_email(email)
            if existing_email_company:
                raise HTTPException(
                    status_code=400,
                    detail="Company email already exists"
                )

            # Prepare company data according to the specification
            company_doc_data = {
                "name": company_data["name"],
                "domain": company_data.get("domain"),  # Optional
                "industry": company_data.get("industry"),  # Optional
                "size": company_data.get("size"),  # Optional
                "createdAt": firestore.SERVER_TIMESTAMP,
                "updatedAt": firestore.SERVER_TIMESTAMP,
                "status": company_data.get("status", "active"),  # Default: "active"
                "billingAccount": company_data.get("billingAccount"),  # Optional
                "address": company_data["address"],  # Required
                "phone": company_data["phone"],  # Required
                "email": email,  # Required - Primary contact email for the company
                "recruivaEmail": company_data.get("recruivaEmail"),  # Optional - Associated @recruiva.ai email
                "settings": company_data.get("settings", {})  # Default: {}
            }

            # Validate recruivaEmail if provided
            if company_doc_data.get("recruivaEmail"):
                recruiva_email = company_doc_data["recruivaEmail"]
                if not recruiva_email.endswith("@recruiva.ai"):
                    raise HTTPException(
                        status_code=400,
                        detail="recruivaEmail must be a @recruiva.ai email address"
                    )

                # Check if recruivaEmail already exists
                existing_recruiva_company = await self._get_company_by_recruiva_email(recruiva_email)
                if existing_recruiva_company:
                    raise HTTPException(
                        status_code=400,
                        detail="Recruiva email already associated with another company"
                    )

            # Validate status
            valid_statuses = ["active", "suspended"]
            if company_doc_data["status"] not in valid_statuses:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid status. Must be one of: {valid_statuses}"
                )

            # Create company document in Firestore
            company_ref = self.db.collection("companies").document()
            company_id = company_ref.id

            # Add the ID to the document data
            company_doc_data["id"] = company_id

            # Save to Firestore
            company_ref.set(company_doc_data)

            logger.info(f"Successfully created company: {company_id}")

            # Return the created company data
            return {
                **company_doc_data,
                "id": company_id
            }

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error creating company: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create company: {str(e)}"
            )

    async def get_company(self, company_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a company by ID.

        Args:
            company_id: The company ID

        Returns:
            Dictionary containing company data or None if not found
        """
        try:
            logger.debug(f"Getting company: {company_id}")

            company_ref = self.db.collection("companies").document(company_id)
            company_doc = company_ref.get()

            if not company_doc.exists:
                return None

            company_data = company_doc.to_dict()
            company_data["id"] = company_id

            return company_data

        except Exception as e:
            logger.error(f"Error getting company {company_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get company: {str(e)}"
            )

    async def update_company(self, company_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing company.

        Args:
            company_id: The company ID
            updates: Dictionary containing fields to update

        Returns:
            Dictionary containing the updated company data

        Raises:
            HTTPException: If company not found or update fails
        """
        try:
            logger.debug(f"Updating company {company_id} with data: {updates}")

            # Check if company exists
            existing_company = await self.get_company(company_id)
            if not existing_company:
                raise HTTPException(
                    status_code=404,
                    detail="Company not found"
                )

            # Validate updates
            if "name" in updates and updates["name"] != existing_company["name"]:
                # Check if new name already exists
                existing_name_company = await self._get_company_by_name(updates["name"])
                if existing_name_company and existing_name_company["id"] != company_id:
                    raise HTTPException(
                        status_code=400,
                        detail="Company name already exists"
                    )

            if "email" in updates and updates["email"] != existing_company["email"]:
                # Check if new email already exists
                existing_email_company = await self._get_company_by_email(updates["email"])
                if existing_email_company and existing_email_company["id"] != company_id:
                    raise HTTPException(
                        status_code=400,
                        detail="Company email already exists"
                    )

            if "recruivaEmail" in updates:
                recruiva_email = updates["recruivaEmail"]
                if recruiva_email and not recruiva_email.endswith("@recruiva.ai"):
                    raise HTTPException(
                        status_code=400,
                        detail="recruivaEmail must be a @recruiva.ai email address"
                    )

                if recruiva_email and recruiva_email != existing_company.get("recruivaEmail"):
                    # Check if new recruivaEmail already exists
                    existing_recruiva_company = await self._get_company_by_recruiva_email(recruiva_email)
                    if existing_recruiva_company and existing_recruiva_company["id"] != company_id:
                        raise HTTPException(
                            status_code=400,
                            detail="Recruiva email already associated with another company"
                        )

            # Validate status if provided
            if "status" in updates:
                valid_statuses = ["active", "suspended"]
                if updates["status"] not in valid_statuses:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid status. Must be one of: {valid_statuses}"
                    )

            # Add updatedAt timestamp
            updates["updatedAt"] = firestore.SERVER_TIMESTAMP

            # Update company in Firestore
            company_ref = self.db.collection("companies").document(company_id)
            company_ref.update(updates)

            logger.info(f"Successfully updated company: {company_id}")

            # Return updated company data
            return await self.get_company(company_id)

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error updating company {company_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to update company: {str(e)}"
            )

    async def list_companies(
        self,
        limit: Optional[int] = None,
        status_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        List all companies with optional filtering.

        Args:
            limit: Maximum number of companies to return
            status_filter: Filter by company status (active, suspended)

        Returns:
            List of company dictionaries
        """
        try:
            logger.debug(f"Listing companies with limit: {limit}, status_filter: {status_filter}")

            # Start with base query
            query = self.db.collection("companies")

            # Apply status filter if provided
            if status_filter:
                valid_statuses = ["active", "suspended"]
                if status_filter not in valid_statuses:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid status filter. Must be one of: {valid_statuses}"
                    )
                query = query.where(filter=FieldFilter("status", "==", status_filter))

            # Order by creation date (most recent first)
            query = query.order_by("createdAt", direction=firestore.Query.DESCENDING)

            # Apply limit if provided
            if limit:
                query = query.limit(limit)

            # Execute query
            docs = query.stream()

            companies = []
            for doc in docs:
                company_data = doc.to_dict()
                company_data["id"] = doc.id
                companies.append(company_data)

            logger.info(f"Found {len(companies)} companies")
            return companies

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error listing companies: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to list companies: {str(e)}"
            )

    async def delete_company(self, company_id: str) -> bool:
        """
        Delete a company.

        Args:
            company_id: The company ID

        Returns:
            True if deletion was successful

        Raises:
            HTTPException: If company not found or deletion fails
        """
        try:
            logger.debug(f"Deleting company: {company_id}")

            # Check if company exists
            existing_company = await self.get_company(company_id)
            if not existing_company:
                raise HTTPException(
                    status_code=404,
                    detail="Company not found"
                )

            # TODO: In a production system, we should check for related data
            # and either prevent deletion or cascade delete appropriately
            # For now, we'll just delete the company document

            # Delete company from Firestore
            company_ref = self.db.collection("companies").document(company_id)
            company_ref.delete()

            logger.info(f"Successfully deleted company: {company_id}")
            return True

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error deleting company {company_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to delete company: {str(e)}"
            )

    # Helper methods for internal use
    async def _get_company_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a company by name."""
        try:
            query = self.db.collection("companies").where(filter=FieldFilter("name", "==", name)).limit(1)
            docs = list(query.stream())

            if docs:
                company_data = docs[0].to_dict()
                company_data["id"] = docs[0].id
                return company_data

            return None

        except Exception as e:
            logger.error(f"Error getting company by name {name}: {str(e)}", exc_info=True)
            return None

    async def _get_company_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get a company by email."""
        try:
            query = self.db.collection("companies").where(filter=FieldFilter("email", "==", email)).limit(1)
            docs = list(query.stream())

            if docs:
                company_data = docs[0].to_dict()
                company_data["id"] = docs[0].id
                return company_data

            return None

        except Exception as e:
            logger.error(f"Error getting company by email {email}: {str(e)}", exc_info=True)
            return None

    async def _get_company_by_recruiva_email(self, recruiva_email: str) -> Optional[Dict[str, Any]]:
        """Get a company by recruiva email."""
        try:
            query = self.db.collection("companies").where(filter=FieldFilter("recruivaEmail", "==", recruiva_email)).limit(1)
            docs = list(query.stream())

            if docs:
                company_data = docs[0].to_dict()
                company_data["id"] = docs[0].id
                return company_data

            return None

        except Exception as e:
            logger.error(f"Error getting company by recruiva email {recruiva_email}: {str(e)}", exc_info=True)
            return None

    async def verify_company_domain(self, company_id: str, domain: str) -> Dict[str, Any]:
        """
        Verify a company domain for SSO/email matching.

        Args:
            company_id: The company ID
            domain: The domain to verify

        Returns:
            Dictionary containing verification result

        Raises:
            HTTPException: If company not found or verification fails
        """
        try:
            logger.debug(f"Verifying domain {domain} for company {company_id}")

            # Check if company exists
            company = await self.get_company(company_id)
            if not company:
                raise HTTPException(
                    status_code=404,
                    detail="Company not found"
                )

            # Basic domain validation
            if not domain or "." not in domain:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid domain format"
                )

            # Check if domain is already associated with another company
            existing_domain_company = await self._get_company_by_domain(domain)
            if existing_domain_company and existing_domain_company["id"] != company_id:
                raise HTTPException(
                    status_code=400,
                    detail="Domain already associated with another company"
                )

            # Update company with verified domain
            await self.update_company(company_id, {"domain": domain})

            logger.info(f"Successfully verified domain {domain} for company {company_id}")

            return {
                "companyId": company_id,
                "domain": domain,
                "verified": True,
                "verifiedAt": datetime.utcnow().isoformat()
            }

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error verifying domain {domain} for company {company_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to verify domain: {str(e)}"
            )

    async def _get_company_by_domain(self, domain: str) -> Optional[Dict[str, Any]]:
        """Get a company by domain."""
        try:
            query = self.db.collection("companies").where(filter=FieldFilter("domain", "==", domain)).limit(1)
            docs = list(query.stream())

            if docs:
                company_data = docs[0].to_dict()
                company_data["id"] = docs[0].id
                return company_data

            return None

        except Exception as e:
            logger.error(f"Error getting company by domain {domain}: {str(e)}", exc_info=True)
            return None

    async def get_company_settings(self, company_id: str) -> Dict[str, Any]:
        """
        Get company settings.

        Args:
            company_id: The company ID

        Returns:
            Dictionary containing company settings

        Raises:
            HTTPException: If company not found
        """
        try:
            company = await self.get_company(company_id)
            if not company:
                raise HTTPException(
                    status_code=404,
                    detail="Company not found"
                )

            return company.get("settings", {})

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error getting company settings {company_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get company settings: {str(e)}"
            )

    async def update_company_settings(self, company_id: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update company settings.

        Args:
            company_id: The company ID
            settings: Dictionary containing settings to update

        Returns:
            Dictionary containing updated company data

        Raises:
            HTTPException: If company not found or update fails
        """
        try:
            # Check if company exists
            company = await self.get_company(company_id)
            if not company:
                raise HTTPException(
                    status_code=404,
                    detail="Company not found"
                )

            # Merge with existing settings
            current_settings = company.get("settings", {})
            updated_settings = {**current_settings, **settings}

            # Update company with new settings
            return await self.update_company(company_id, {"settings": updated_settings})

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error updating company settings {company_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to update company settings: {str(e)}"
            )
