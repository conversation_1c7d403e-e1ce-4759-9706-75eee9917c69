"""
Model configuration management for OpenAI models.
This module centralizes all model-specific parameters and configurations.
"""

from typing import Dict, Any, Optional, List
from app.core.config import settings

class ModelConfigurationManager:
    """
    Manages configuration profiles for different OpenAI models and use cases.
    Centralizes parameter management to make it easier to update and optimize.
    """

    # Default model configurations
    DEFAULT_CONFIGS = {
        # Chat models
        "gpt-4.1": {
            "temperature": 0.7,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "max_tokens": 4096,
            "timeout": 30,
        },
        "gpt-4o": {
            "temperature": 0.7,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "max_tokens": 4096,
            "timeout": 30,
        },
        "gpt-4o-mini": {
            "temperature": 0.7,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "max_tokens": 4096,
            "timeout": 20,
        },
        "gpt-4-turbo": {
            "temperature": 0.7,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "max_tokens": 4096,
            "timeout": 30,
        },
        "gpt-3.5-turbo": {
            "temperature": 0.8,  # Slightly higher temperature for more creative responses
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "max_tokens": 4096,
            "timeout": 15,
        },
        # Realtime models
        "gpt-4o-realtime-preview-2024-12-17": {
            "temperature": 0.6,
            "timeout": 30,
        },
    }

    # Use case specific configurations that override the defaults
    USE_CASE_CONFIGS = {
        "interview_agent": {
            "gpt-4.1": {
                "temperature": 0.7,  # Lower temperature for more consistent interview responses
                "max_tokens": 2048,  # Shorter responses for interview context
            },
            "gpt-4o": {
                "temperature": 0.7,  # Lower temperature for more consistent interview responses
                "max_tokens": 2048,  # Shorter responses for interview context
            },
            "gpt-4o-mini": {
                "temperature": 0.6,
                "max_tokens": 2048,
            },
            "gpt-4o-realtime-preview-2024-12-17": {
                "temperature": 0.6,
            }
        },
        "interview_question_agent": {
            "gpt-4.1": {
                "temperature": 0.8,  # Balanced for creative yet relevant questions
                "max_tokens": 4096,  # Full context for detailed questions
                "presence_penalty": 0.1,  # Slight penalty to avoid repetitive questions
                "frequency_penalty": 0.1,  # Slight penalty to encourage diverse questions
            },
            "gpt-4o": {
                "temperature": 0.8,  # Balanced for creative yet relevant questions
                "max_tokens": 4096,  # Full context for detailed questions
                "presence_penalty": 0.1,  # Slight penalty to avoid repetitive questions
                "frequency_penalty": 0.1,  # Slight penalty to encourage diverse questions
            },
            "gpt-4-turbo": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "presence_penalty": 0.1,
                "frequency_penalty": 0.1,
            },
            "gpt-3.5-turbo": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "presence_penalty": 0.1,
                "frequency_penalty": 0.1,
            }
        },
        "criterion_agent": {
            "gpt-4.1": {
                "temperature": 0.7,  # Balanced for creative yet structured evaluation criteria
                "max_tokens": 4096,  # Full context for detailed criteria
                "presence_penalty": 0.1,  # Slight penalty to avoid repetitive criteria
                "frequency_penalty": 0.1,  # Slight penalty to encourage diverse criteria
            },
            "gpt-4o": {
                "temperature": 0.7,  # Balanced for creative yet structured evaluation criteria
                "max_tokens": 4096,  # Full context for detailed criteria
                "presence_penalty": 0.1,  # Slight penalty to avoid repetitive criteria
                "frequency_penalty": 0.1,  # Slight penalty to encourage diverse criteria
            },
            "gpt-4-turbo": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "presence_penalty": 0.1,
                "frequency_penalty": 0.1,
            },
            "gpt-3.5-turbo": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "presence_penalty": 0.1,
                "frequency_penalty": 0.1,
            }
        },
        "intake_agent": {
            "gpt-4.1": {
                "temperature": 0.7,  # Balanced for intake conversations
                "max_tokens": 4096,  # Full context for detailed intake
            },
            "gpt-4o": {
                "temperature": 0.7,  # Balanced for intake conversations
                "max_tokens": 4096,  # Full context for detailed intake
            },
            "gpt-4o-mini": {
                "temperature": 0.7,
                "max_tokens": 4096,
            },
            "gpt-4o-realtime-preview-2024-12-17": {
                "temperature": 0.6,
            }
        },
        "email_agent": {
            "gpt-4.1": {
                "temperature": 0.7,  # Very low temperature for more deterministic function calling
                "max_tokens": 4096,  # Increased tokens for handling complex emails
                "presence_penalty": 0.1,  # Slight penalty to avoid repetition
                "frequency_penalty": 0.1,  # Slight penalty to avoid repetition
            },
            "gpt-4o": {
                "temperature": 0.7,  # Very low temperature for more deterministic function calling
                "max_tokens": 4096,  # Increased tokens for handling complex emails
                "presence_penalty": 0.1,  # Slight penalty to avoid repetition
                "frequency_penalty": 0.1,  # Slight penalty to avoid repetition
            },
            "gpt-4o-mini": {
                "temperature": 0.7,  # Low temperature for more deterministic function calling
                "max_tokens": 4096,  # Increased tokens for handling complex emails
                "presence_penalty": 0.1,
                "frequency_penalty": 0.1,
            },
            "gpt-4-turbo": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "presence_penalty": 0.1,
                "frequency_penalty": 0.1,
            },
            "gpt-3.5-turbo": {
                "temperature": 0.6,
                "max_tokens": 4096,
                "presence_penalty": 0.1,
                "frequency_penalty": 0.1,
            }
        },
        "job_posting": {
            "gpt-4.1": {
                "temperature": 0.7,
                "max_tokens": 4096,  # Increased tokens for detailed job posting
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
            },
            "gpt-4o": {
                "temperature": 0.7,
                "max_tokens": 4096,  # Increased tokens for detailed job posting
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
            },
            "gpt-4-turbo": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
            },
            "gpt-3.5-turbo": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
            }
        },
        "role_enrichment": {
            "gpt-4.1": {
                "temperature": 0.6,
                "max_tokens": 4096,  # Increased tokens for detailed role enrichment
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
            },
            "gpt-4o": {
                "temperature": 0.6,
                "max_tokens": 4096,  # Increased tokens for detailed role enrichment
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
            },
            "gpt-4-turbo": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
            },
            "gpt-3.5-turbo": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
            }
        },
        "json_response": {
            "gpt-4.1": {
                "temperature": 0.3,  # Lower temperature for more deterministic JSON responses
                "response_format": {"type": "json_object"},
            },
            "gpt-4o": {
                "temperature": 0.3,  # Lower temperature for more deterministic JSON responses
                "response_format": {"type": "json_object"},
            },
            "gpt-4-turbo": {
                "temperature": 0.3,
                "response_format": {"type": "json_object"},
            }
        }
    }

    # List of realtime models
    REALTIME_MODELS = [
        "gpt-4o-realtime-preview-2024-12-17"
    ]

    # Mapping of use cases to appropriate model types
    USE_CASE_MODEL_TYPES = {
        "intake_agent": "realtime",
        "interview_agent": "realtime",
        "interview_question_agent": "chat",
        "criterion_agent": "chat",
        "email_agent": "chat",
        "job_posting": "chat",
        "role_enrichment": "chat"
    }

    @classmethod
    def is_realtime_model(cls, model: str) -> bool:
        """
        Check if a model is a realtime model.

        Args:
            model: The model identifier

        Returns:
            True if the model is a realtime model, False otherwise
        """
        return model in cls.REALTIME_MODELS

    @classmethod
    def get_appropriate_model(cls, use_case: str) -> str:
        """
        Get the appropriate model for a specific use case.

        Args:
            use_case: The use case identifier

        Returns:
            The appropriate model for the use case
        """
        model_type = cls.USE_CASE_MODEL_TYPES.get(use_case, "chat")

        if model_type == "realtime":
            # For realtime use cases, use the realtime model
            return "gpt-4o-realtime-preview-2024-12-17"
        else:
            # For chat use cases, use the default chat model
            return settings.OPENAI_MODELS_CONFIG["chat"]["default"]

    @classmethod
    def get_model_config(cls, model: str, use_case: Optional[str] = None) -> Dict[str, Any]:
        """
        Get configuration parameters for a specific model and use case.

        Args:
            model: The model identifier (e.g., "gpt-4o")
            use_case: Optional use case identifier (e.g., "interview_agent")

        Returns:
            Dictionary of configuration parameters
        """
        # Start with default configuration for the model
        if model in cls.DEFAULT_CONFIGS:
            config = cls.DEFAULT_CONFIGS[model].copy()
        else:
            # If model not found, use the default model's config
            default_model = settings.OPENAI_DEFAULT_MODEL
            config = cls.DEFAULT_CONFIGS.get(default_model, {}).copy()

        # Apply use case specific overrides if applicable
        if use_case and use_case in cls.USE_CASE_CONFIGS:
            use_case_config = cls.USE_CASE_CONFIGS[use_case]
            if model in use_case_config:
                config.update(use_case_config[model])
            elif default_model in use_case_config:
                # Fall back to default model config for this use case
                config.update(use_case_config[default_model])

        return config

    @classmethod
    def get_fallback_model(cls, model: str) -> str:
        """
        Get the appropriate fallback model for a given model.

        Args:
            model: The primary model identifier

        Returns:
            Fallback model identifier
        """
        fallback_map = {
            "gpt-4.1": "gpt-4-turbo",
            "gpt-4o": "gpt-4-turbo",
            "gpt-4o-mini": "gpt-3.5-turbo",
            "gpt-4-turbo": "gpt-3.5-turbo",
        }

        return fallback_map.get(model, settings.OPENAI_MODELS_CONFIG["chat"]["fallback"])