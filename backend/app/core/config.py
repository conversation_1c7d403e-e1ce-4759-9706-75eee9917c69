# File: backend/app/core/config.py

from pydantic_settings import BaseSettings
from typing import List, Optional, Dict, Any, ClassVar
import os
from pydantic import validator

def get_environment() -> str:
    """Detect environment from various sources."""
    # Priority: ENV var > RENDER env > default
    env = os.getenv("ENVIRONMENT", os.getenv("RENDER_SERVICE_NAME", "development"))

    # Map Render service names to environments
    if "dev" in env.lower():
        return "development"
    elif "demo" in env.lower():
        return "demo"
    elif "prod" in env.lower() or env == "recruiva":
        return "production"
    else:
        return "development"

def load_environment_config(environment: str) -> Dict[str, Any]:
    """Load environment-specific configuration."""
    try:
        if environment == "development":
            import config.dev as dev_config
            return {
                "FIREBASE_PROJECT_ID": dev_config.FIREBASE_PROJECT_ID,
                "FIREBASE_STORAGE_BUCKET": dev_config.FIREBASE_STORAGE_BUCKET,
                "API_URL": dev_config.API_URL,
                "FRONTEND_URL": dev_config.FRONTEND_URL,
                "DEBUG": dev_config.DEBUG
            }
        elif environment == "demo":
            import config.demo as demo_config
            return {
                "FIREBASE_PROJECT_ID": demo_config.FIREBASE_PROJECT_ID,
                "FIREBASE_STORAGE_BUCKET": demo_config.FIREBASE_STORAGE_BUCKET,
                "API_URL": demo_config.API_URL,
                "FRONTEND_URL": demo_config.FRONTEND_URL,
                "DEBUG": demo_config.DEBUG
            }
        elif environment == "production":
            import config.prod as prod_config
            return {
                "FIREBASE_PROJECT_ID": prod_config.FIREBASE_PROJECT_ID,
                "FIREBASE_STORAGE_BUCKET": prod_config.FIREBASE_STORAGE_BUCKET,
                "API_URL": prod_config.API_URL,
                "FRONTEND_URL": prod_config.FRONTEND_URL,
                "DEBUG": prod_config.DEBUG
            }
        else:
            return {}
    except ImportError as e:
        print(f"Warning: Could not load environment config for {environment}: {e}")
        return {}

class Settings(BaseSettings):
    PROJECT_NAME: str = "Recruiva"
    API_V1_STR: str = "/api/v1"

    # Environment Configuration - Auto-detected
    ENVIRONMENT: str = get_environment()
    DEBUG: bool = False
    PORT: int = 8000

    # Service URLs - Will be overridden by environment config
    API_URL: str = "http://localhost:8000"
    FRONTEND_URL: str = "http://localhost:3000"
    INTERNAL_API_URL: str = "http://127.0.0.1:8000"  # For internal service communication

    # Firebase Admin SDK Configuration
    FIREBASE_PROJECT_ID: str
    FIREBASE_PRIVATE_KEY_ID: str
    FIREBASE_PRIVATE_KEY: str
    FIREBASE_CLIENT_EMAIL: str
    FIREBASE_CLIENT_ID: str
    FIREBASE_STORAGE_BUCKET: str

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Load environment-specific configuration
        env_config = load_environment_config(self.ENVIRONMENT)
        for key, value in env_config.items():
            if hasattr(self, key):
                setattr(self, key, value)

    @property
    def get_api_url(self) -> str:
        """Get the API URL based on environment."""
        return self.API_URL

    @property
    def get_frontend_url(self) -> str:
        """Get the frontend URL based on environment."""
        return self.FRONTEND_URL

    @validator("FIREBASE_PRIVATE_KEY", pre=True)
    def handle_private_key(cls, v: str) -> str:
        """Handle private key formatting with newlines."""
        if isinstance(v, str):
            # Replace escaped newlines with actual newlines
            return v.replace('\\n', '\n')
        return v

    # Firebase Config
    FIREBASE_API_KEY: str
    FIREBASE_AUTH_DOMAIN: str
    FIREBASE_MESSAGING_SENDER_ID: str
    FIREBASE_APP_ID: str
    FIREBASE_MEASUREMENT_ID: str

    # Google OAuth Configuration
    GOOGLE_CLIENT_ID: str
    GOOGLE_CLIENT_SECRET: str

    # OpenAI Configuration
    OPENAI_API_KEY: str
    OPENAI_ORG_ID: Optional[str] = None
    OPENAI_DEFAULT_MODEL: str = "gpt-4.1"
    OPENAI_TIMEOUT_SECONDS: int = 30
    OPENAI_MAX_RETRIES: int = 3

    # OpenAI Models Config
    OPENAI_MODELS_CONFIG: ClassVar[Dict[str, Dict[str, Any]]] = {
        "chat": {
            "default": "gpt-4.1",
            "fallback": "gpt-4o"
        },
        "vision": {
            "default": "gpt-4-vision-preview"
        },
        "audio": {
            "transcription": "whisper-1",
            "synthesis": "tts-1"
        }
    }

    # Test Configuration
    TEST_EMAIL_PASSWORD: Optional[str] = None
    TEST_EMAIL_ADDRESS: str = "<EMAIL>"

    # Email Configuration
    EMAIL_USERNAME: str
    EMAIL_PASSWORD: str
    EMAIL_FROM: str = "<EMAIL>"
    ADMIN_EMAIL: str = "<EMAIL>"

    # Email Listener Configuration
    # TEMPORARILY DISABLED: Email listener has been disabled.
    # To re-enable the email service:
    # 1. Change EMAIL_LISTENER_ENABLED to True
    # 2. Restart the application
    # This will resume automatic checking and processing of emails from the mailbox.
    EMAIL_LISTENER_ENABLED: bool = False  # Temporarily disabled
    EMAIL_CHECK_INTERVAL: int = 60  # seconds
    EMAIL_MAX_AGE: int = 86400  # 24 hours in seconds
    ALLOWED_EMAIL_DOMAINS: List[str] = ["recruiva.ai"]

    # Database
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "recruiva"
    SQLALCHEMY_DATABASE_URI: Optional[str] = None

    @property
    def get_database_url(self) -> str:
        if self.SQLALCHEMY_DATABASE_URI:
            return self.SQLALCHEMY_DATABASE_URI
        return f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}/{self.POSTGRES_DB}"

    # Monitoring Configuration
    ENABLE_EMAIL_MONITORING: bool = True
    ERROR_NOTIFICATION_EMAIL: str = "<EMAIL>"

    # OpenAI Configuration
    OPENAI_CONFIG: ClassVar[Dict[str, Dict[str, Any]]] = {
        "models": {
            "chat": {
                "default": "gpt-4.1",
                "fallback": "gpt-4o"
            },
            "voice": {
                "default": "whisper-1"
            }
        },
        "temperature": {
            "email": 0.7,
            "voice": 0.5
        },
        "max_tokens": {
            "email": 2000,
            "voice": 500
        }
    }

    # Prompt Configuration
    PROMPT_CONFIG: ClassVar[Dict[str, Any]] = {
        "base_path": "app/services/openai/prompts",
        "default_context": "chat",
        "default_role": "email_processor"
    }

    # Email Configuration
    EMAIL_CONFIG: ClassVar[Dict[str, Any]] = {
        "workspace_email": "<EMAIL>",
        "allowed_domains": ["recruiva.ai"],
        "email_check_interval": 60,  # seconds
        "max_email_age": 24 * 60 * 60  # 24 hours
    }

    # Google Calendar API Configuration
    GOOGLE_CALENDAR_CREDENTIALS: Optional[Dict[str, Any]] = None

    @property
    def APP_CONFIG(self) -> Dict[str, Any]:
        return {
            "environment": self.ENVIRONMENT,
            "debug": self.DEBUG,
            "frontend_url": self.get_frontend_url,
            "api_url": self.get_api_url,
            "port": self.PORT
        }

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
settings.SQLALCHEMY_DATABASE_URI = settings.get_database_url
