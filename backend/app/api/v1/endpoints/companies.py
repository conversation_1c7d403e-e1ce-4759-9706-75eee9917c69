# File: backend/app/api/v1/endpoints/companies.py

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
import logging
from app.services.company_service import CompanyService
from app.api.v1.models import (
    CompanyCreate,
    CompanyUpdate,
    CompanyResponse,
    CompanyDomainVerification,
    CompanySettingsUpdate,
    CompanyListResponse,
    DomainVerificationResponse
)
from app.core.auth import get_current_user

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/companies")

# Initialize service
company_service = CompanyService()

@router.post("/", response_model=CompanyResponse, status_code=201)
async def create_company(
    company_data: CompanyCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new company.

    This endpoint creates a new company following the exact schema from
    firebase_database_design_specification.md.
    """
    try:
        logger.info(f"Creating company: {company_data.name}")

        # Convert Pydantic model to dict
        company_dict = company_data.dict()

        # Create company
        created_company = await company_service.create_company(company_dict)

        logger.info(f"Successfully created company: {created_company['id']}")
        return CompanyResponse(**created_company)

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error creating company: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create company: {str(e)}"
        )

@router.get("/{company_id}", response_model=CompanyResponse)
async def get_company(
    company_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get a company by ID.
    """
    try:
        logger.info(f"Getting company: {company_id}")

        company = await company_service.get_company(company_id)
        if not company:
            raise HTTPException(
                status_code=404,
                detail="Company not found"
            )

        return CompanyResponse(**company)

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error getting company {company_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get company: {str(e)}"
        )

@router.put("/{company_id}", response_model=CompanyResponse)
async def update_company(
    company_id: str,
    company_updates: CompanyUpdate,
    current_user: dict = Depends(get_current_user)
):
    """
    Update an existing company.
    """
    try:
        logger.info(f"Updating company: {company_id}")

        # Convert Pydantic model to dict, excluding None values
        updates_dict = company_updates.dict(exclude_none=True)

        if not updates_dict:
            raise HTTPException(
                status_code=400,
                detail="No updates provided"
            )

        # Update company
        updated_company = await company_service.update_company(company_id, updates_dict)

        logger.info(f"Successfully updated company: {company_id}")
        return CompanyResponse(**updated_company)

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error updating company {company_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update company: {str(e)}"
        )

@router.get("/", response_model=CompanyListResponse)
async def list_companies(
    limit: Optional[int] = Query(None, description="Maximum number of companies to return"),
    status: Optional[str] = Query(None, description="Filter by company status (active, suspended)"),
    current_user: dict = Depends(get_current_user)
):
    """
    List all companies with optional filtering.
    """
    try:
        logger.info(f"Listing companies with limit: {limit}, status: {status}")

        companies = await company_service.list_companies(
            limit=limit,
            status_filter=status
        )

        # Convert to response models
        company_responses = [CompanyResponse(**company) for company in companies]

        return CompanyListResponse(
            companies=company_responses,
            total=len(company_responses)
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error listing companies: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list companies: {str(e)}"
        )

@router.delete("/{company_id}", status_code=204)
async def delete_company(
    company_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a company.
    """
    try:
        logger.info(f"Deleting company: {company_id}")

        await company_service.delete_company(company_id)

        logger.info(f"Successfully deleted company: {company_id}")
        return None

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error deleting company {company_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete company: {str(e)}"
        )

@router.post("/{company_id}/verify-domain", response_model=DomainVerificationResponse)
async def verify_company_domain(
    company_id: str,
    domain_data: CompanyDomainVerification,
    current_user: dict = Depends(get_current_user)
):
    """
    Verify a company domain for SSO/email matching.
    """
    try:
        logger.info(f"Verifying domain {domain_data.domain} for company: {company_id}")

        verification_result = await company_service.verify_company_domain(
            company_id,
            domain_data.domain
        )

        logger.info(f"Successfully verified domain for company: {company_id}")
        return DomainVerificationResponse(**verification_result)

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error verifying domain for company {company_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to verify domain: {str(e)}"
        )

@router.get("/{company_id}/settings")
async def get_company_settings(
    company_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get company settings.
    """
    try:
        logger.info(f"Getting settings for company: {company_id}")

        settings = await company_service.get_company_settings(company_id)

        return {"settings": settings}

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error getting company settings {company_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get company settings: {str(e)}"
        )

@router.put("/{company_id}/settings", response_model=CompanyResponse)
async def update_company_settings(
    company_id: str,
    settings_data: CompanySettingsUpdate,
    current_user: dict = Depends(get_current_user)
):
    """
    Update company settings.
    """
    try:
        logger.info(f"Updating settings for company: {company_id}")

        updated_company = await company_service.update_company_settings(
            company_id,
            settings_data.settings
        )

        logger.info(f"Successfully updated settings for company: {company_id}")
        return CompanyResponse(**updated_company)

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error updating company settings {company_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update company settings: {str(e)}"
        )
