# File: backend/app/api/v1/api.py

from fastapi import APIRouter
from app.api.v1.endpoints import (
    roles,
    realtime,
    chat_completion,
    templates,
    resume_evaluation,
    interview_evaluation,
    applications,
    dashboard,
    evaluation_fix,
    interview_evaluation_debug,
    companies
)

api_router = APIRouter()

# Include active routers with standardized prefixes (no trailing slashes)
api_router.include_router(roles.router, tags=["roles"])  # prefix already set in roles.router
api_router.include_router(roles.public_router, tags=["public-roles"])  # Include public router
api_router.include_router(realtime.router, prefix="/realtime", tags=["realtime"])
api_router.include_router(chat_completion.router, prefix="/chat-completion", tags=["chat-completion"])
api_router.include_router(templates.router, tags=["templates"])  # prefix already set in templates.router
api_router.include_router(resume_evaluation.router, tags=["resume-evaluation"])  # prefix already set in resume_evaluation.router
api_router.include_router(interview_evaluation.router, prefix="/interview-evaluation", tags=["interview-evaluation"])
api_router.include_router(interview_evaluation_debug.router, prefix="/interview-evaluation", tags=["interview-evaluation-debug"])
api_router.include_router(applications.router, prefix="/public", tags=["applications"])  # Add applications router
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])  # Add dashboard router
api_router.include_router(evaluation_fix.router, prefix="/evaluation-fix", tags=["evaluation-fix"])  # Add evaluation fix router
api_router.include_router(companies.router, tags=["companies"])  # Add companies router