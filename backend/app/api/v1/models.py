from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, EmailStr, validator

class ClientSecret(BaseModel):
    value: str
    expires_at: int

class PublicInterviewSessionRequest(BaseModel):
    role_id: str
    template_id: Optional[str] = None
    candidate_id: Optional[str] = Field(None, description="ID of the candidate (usually email)")
    application_id: Optional[str] = Field(None, description="ID of the application")
    resume_text: Optional[str] = Field(None, description="Resume text for context")
    job_posting: Optional[str] = Field(None, description="Job posting text for context")
    candidate_name: Optional[str] = Field(None, description="Name of the candidate")

class SessionResponse(BaseModel):
    session_id: str
    client_secret: ClientSecret
    transcript_id: Optional[str] = None
    role_id: Optional[str] = None
    template_id: Optional[str] = None
    candidate_id: Optional[str] = None
    application_id: Optional[str] = None
    is_public: bool = False

# Company Models following the Firebase database design specification

class CompanyCreate(BaseModel):
    """Model for creating a new company."""
    name: str = Field(..., description="Company name", min_length=1, max_length=255)
    domain: Optional[str] = Field(None, description="Company domain (for SSO/email)")
    industry: Optional[str] = Field(None, description="Company industry")
    size: Optional[str] = Field(None, description="Company size range")
    address: str = Field(..., description="Mailing address", min_length=1)
    phone: str = Field(..., description="Phone number", min_length=1)
    email: EmailStr = Field(..., description="Primary contact email for the company")
    recruivaEmail: Optional[EmailStr] = Field(None, description="Associated @recruiva.ai email address")
    billingAccount: Optional[str] = Field(None, description="Billing account number")
    settings: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Company preferences/settings")
    status: Optional[str] = Field("active", description="Company status (active, suspended)")

    @validator('recruivaEmail')
    def validate_recruiva_email(cls, v):
        if v and not v.endswith('@recruiva.ai'):
            raise ValueError('recruivaEmail must be a @recruiva.ai email address')
        return v

    @validator('status')
    def validate_status(cls, v):
        valid_statuses = ['active', 'suspended']
        if v not in valid_statuses:
            raise ValueError(f'Status must be one of: {valid_statuses}')
        return v

class CompanyUpdate(BaseModel):
    """Model for updating an existing company."""
    name: Optional[str] = Field(None, description="Company name", min_length=1, max_length=255)
    domain: Optional[str] = Field(None, description="Company domain (for SSO/email)")
    industry: Optional[str] = Field(None, description="Company industry")
    size: Optional[str] = Field(None, description="Company size range")
    address: Optional[str] = Field(None, description="Mailing address", min_length=1)
    phone: Optional[str] = Field(None, description="Phone number", min_length=1)
    email: Optional[EmailStr] = Field(None, description="Primary contact email for the company")
    recruivaEmail: Optional[EmailStr] = Field(None, description="Associated @recruiva.ai email address")
    billingAccount: Optional[str] = Field(None, description="Billing account number")
    settings: Optional[Dict[str, Any]] = Field(None, description="Company preferences/settings")
    status: Optional[str] = Field(None, description="Company status (active, suspended)")

    @validator('recruivaEmail')
    def validate_recruiva_email(cls, v):
        if v and not v.endswith('@recruiva.ai'):
            raise ValueError('recruivaEmail must be a @recruiva.ai email address')
        return v

    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            valid_statuses = ['active', 'suspended']
            if v not in valid_statuses:
                raise ValueError(f'Status must be one of: {valid_statuses}')
        return v

class CompanyResponse(BaseModel):
    """Model for company response data."""
    id: str = Field(..., description="Unique company identifier")
    name: str = Field(..., description="Company name")
    domain: Optional[str] = Field(None, description="Company domain (for SSO/email)")
    industry: Optional[str] = Field(None, description="Company industry")
    size: Optional[str] = Field(None, description="Company size range")
    address: str = Field(..., description="Mailing address")
    phone: str = Field(..., description="Phone number")
    email: str = Field(..., description="Primary contact email for the company")
    recruivaEmail: Optional[str] = Field(None, description="Associated @recruiva.ai email address")
    billingAccount: Optional[str] = Field(None, description="Billing account number")
    settings: Dict[str, Any] = Field(default_factory=dict, description="Company preferences/settings")
    status: str = Field(..., description="Company status (active, suspended)")
    createdAt: Any = Field(..., description="Creation timestamp")
    updatedAt: Any = Field(..., description="Last update timestamp")

class CompanyDomainVerification(BaseModel):
    """Model for company domain verification."""
    domain: str = Field(..., description="Domain to verify", min_length=1)

    @validator('domain')
    def validate_domain(cls, v):
        if not v or '.' not in v:
            raise ValueError('Invalid domain format')
        return v

class CompanySettingsUpdate(BaseModel):
    """Model for updating company settings."""
    settings: Dict[str, Any] = Field(..., description="Settings to update")

class CompanyListResponse(BaseModel):
    """Model for company list response."""
    companies: list[CompanyResponse] = Field(..., description="List of companies")
    total: int = Field(..., description="Total number of companies")

class DomainVerificationResponse(BaseModel):
    """Model for domain verification response."""
    companyId: str = Field(..., description="Company ID")
    domain: str = Field(..., description="Verified domain")
    verified: bool = Field(..., description="Verification status")
    verifiedAt: str = Field(..., description="Verification timestamp")