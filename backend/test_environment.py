#!/usr/bin/env python3
"""
Test script to verify environment detection and configuration loading.
"""

import os
import sys
sys.path.append('.')

from app.core.config import settings, get_environment, load_environment_config

def test_environment_detection():
    """Test environment detection logic."""
    print("=== Environment Detection Test ===")
    
    # Test current environment
    current_env = get_environment()
    print(f"Detected environment: {current_env}")
    
    # Test environment config loading
    env_config = load_environment_config(current_env)
    print(f"Environment config: {env_config}")
    
    # Test settings initialization
    print(f"Settings environment: {settings.ENVIRONMENT}")
    print(f"Settings Firebase project: {settings.FIREBASE_PROJECT_ID}")
    print(f"Settings API URL: {settings.API_URL}")
    print(f"Settings Frontend URL: {settings.FRONTEND_URL}")
    print(f"Settings Debug: {settings.DEBUG}")
    
    return True

def test_different_environments():
    """Test different environment scenarios."""
    print("\n=== Testing Different Environment Scenarios ===")
    
    # Save original environment
    original_env = os.environ.get("ENVIRONMENT")
    original_render = os.environ.get("RENDER_SERVICE_NAME")
    
    test_cases = [
        ("development", None, "development"),
        ("demo", None, "demo"),
        ("production", None, "production"),
        (None, "recruiva-dev", "development"),
        (None, "recruiva-demo", "demo"),
        (None, "recruiva", "production"),
        (None, "recruiva-prod", "production"),
    ]
    
    for env_var, render_var, expected in test_cases:
        # Set test environment
        if env_var:
            os.environ["ENVIRONMENT"] = env_var
        elif "ENVIRONMENT" in os.environ:
            del os.environ["ENVIRONMENT"]
            
        if render_var:
            os.environ["RENDER_SERVICE_NAME"] = render_var
        elif "RENDER_SERVICE_NAME" in os.environ:
            del os.environ["RENDER_SERVICE_NAME"]
        
        # Test detection
        detected = get_environment()
        status = "✓" if detected == expected else "✗"
        print(f"{status} ENV={env_var}, RENDER={render_var} -> Expected: {expected}, Got: {detected}")
    
    # Restore original environment
    if original_env:
        os.environ["ENVIRONMENT"] = original_env
    elif "ENVIRONMENT" in os.environ:
        del os.environ["ENVIRONMENT"]
        
    if original_render:
        os.environ["RENDER_SERVICE_NAME"] = original_render
    elif "RENDER_SERVICE_NAME" in os.environ:
        del os.environ["RENDER_SERVICE_NAME"]

if __name__ == "__main__":
    try:
        test_environment_detection()
        test_different_environments()
        print("\n✓ All tests completed successfully!")
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
