# Phase 1 Testing Guide - Multi-Environment Setup

This guide provides comprehensive testing instructions for Phase 1 of the Firebase optimization implementation plan.

## Prerequisites

- Docker Desktop installed and running
- Node.js 18+ installed
- Python 3.11+ with virtual environment
- Access to Firebase projects: recruiva-dev, recruiva-demo, recruiva (production)

## 1. Backend Environment Detection Testing

### Test Backend Environment Detection
```bash
cd backend
source venv/bin/activate
python test_environment.py
```

**Expected Output:**
- Environment detection should work for development/demo/production
- Configuration should load correctly for each environment
- Firebase project should be set to `recruiva-dev` for development

### Test Backend with Different Environments
```bash
# Test development environment
ENVIRONMENT=development python test_environment.py

# Test demo environment  
ENVIRONMENT=demo python test_environment.py

# Test production environment
ENVIRONMENT=production python test_environment.py
```

## 2. Docker Development Setup Testing

### Build and Test Backend Container
```bash
# Build the development backend container
docker compose -f docker-compose.dev.yml build backend

# Start the backend service
docker compose -f docker-compose.dev.yml up backend

# Test hot reload by modifying a file in backend/app/
# The container should automatically restart
```

**Expected Behavior:**
- Container builds successfully
- Backend starts on port 8000
- Hot reload works when files are modified
- Environment is detected as "development"
- Firebase project is "recruiva-dev"

### Build and Test Frontend Container
```bash
# Build the development frontend container
docker compose -f docker-compose.dev.yml build frontend

# Start the frontend service
docker compose -f docker-compose.dev.yml up frontend

# Test hot reload by modifying a file in frontend/src/
```

**Expected Behavior:**
- Container builds successfully
- Frontend starts on port 3000
- Hot reload works when files are modified
- Environment detection works correctly

### Test Full Stack with Docker
```bash
# Start both services
docker compose -f docker-compose.dev.yml up

# Test API connectivity
curl http://localhost:8000/api/v1/health

# Test frontend
open http://localhost:3000
```

## 3. Environment Configuration Testing

### Test Backend Environment Loading
```bash
cd backend
source venv/bin/activate

# Test development config
ENVIRONMENT=development python -c "
from app.core.config import settings
print(f'Environment: {settings.ENVIRONMENT}')
print(f'Firebase Project: {settings.FIREBASE_PROJECT_ID}')
print(f'API URL: {settings.API_URL}')
print(f'Debug: {settings.DEBUG}')
"

# Test demo config
ENVIRONMENT=demo python -c "
from app.core.config import settings
print(f'Environment: {settings.ENVIRONMENT}')
print(f'Firebase Project: {settings.FIREBASE_PROJECT_ID}')
print(f'API URL: {settings.API_URL}')
print(f'Debug: {settings.DEBUG}')
"
```

### Test Frontend Environment Detection
```bash
cd frontend

# Test development environment
NODE_ENV=development NEXT_PUBLIC_ENVIRONMENT=development npm run build

# Test demo environment
NODE_ENV=production NEXT_PUBLIC_ENVIRONMENT=demo npm run build

# Test production environment
NODE_ENV=production NEXT_PUBLIC_ENVIRONMENT=production npm run build
```

## 4. Firebase Integration Testing

### Test Firebase Connection
```bash
cd backend
source venv/bin/activate

# Test Firebase connection with development project
python -c "
from app.services.firebase_service import FirebaseService
firebase = FirebaseService()
print('Firebase initialized successfully')
print(f'Project ID: {firebase.db._client._project}')
"
```

### Test Environment-Specific Firebase Projects
```bash
# Test development Firebase
ENVIRONMENT=development python -c "
from app.core.config import settings
print(f'Firebase Project: {settings.FIREBASE_PROJECT_ID}')
# Should output: recruiva-dev
"

# Test demo Firebase
ENVIRONMENT=demo python -c "
from app.core.config import settings
print(f'Firebase Project: {settings.FIREBASE_PROJECT_ID}')
# Should output: recruiva-demo
"

# Test production Firebase
ENVIRONMENT=production python -c "
from app.core.config import settings
print(f'Firebase Project: {settings.FIREBASE_PROJECT_ID}')
# Should output: recruiva
"
```

## 5. Local Development Testing

### Test Local Backend (without Docker)
```bash
cd backend
source venv/bin/activate

# Set environment to development
export ENVIRONMENT=development

# Start the backend
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Test health endpoint
curl http://localhost:8000/api/v1/health
```

### Test Local Frontend (without Docker)
```bash
cd frontend

# Set environment to development
export NODE_ENV=development
export NEXT_PUBLIC_ENVIRONMENT=development

# Start the frontend
npm run dev

# Test frontend
open http://localhost:3000
```

## 6. Deployment Environment Testing

### Test Render Environment Detection
```bash
# Simulate Render environment
RENDER_SERVICE_NAME=recruiva-dev python test_environment.py
# Should detect as development

RENDER_SERVICE_NAME=recruiva-demo python test_environment.py
# Should detect as demo

RENDER_SERVICE_NAME=recruiva python test_environment.py
# Should detect as production
```

### Test Vercel Environment Detection
```bash
cd frontend

# Simulate Vercel preview environment
VERCEL_ENV=preview VERCEL_GIT_COMMIT_REF=dev node test-environment.js

# Simulate Vercel production environment
VERCEL_ENV=production VERCEL_URL=www.recruiva.ai node test-environment.js
```

## Expected Results Summary

### ✅ Backend Environment Detection
- [x] Detects development/demo/production correctly
- [x] Loads environment-specific configuration
- [x] Uses correct Firebase project for each environment
- [x] Sets appropriate debug flags

### ✅ Frontend Environment Detection
- [x] Detects environment from various sources
- [x] Loads correct configuration for each environment
- [x] Works with Vercel deployment scenarios

### ✅ Docker Development Setup
- [x] Backend container builds and runs
- [x] Frontend container builds and runs
- [x] Hot reload works for both services
- [x] Environment variables are properly set
- [x] Services can communicate

### ✅ Firebase Integration
- [x] Connects to correct Firebase project per environment
- [x] Environment-specific service account keys work
- [x] Firebase services initialize correctly

## Troubleshooting

### Common Issues

1. **Import errors in backend**: Make sure virtual environment is activated
2. **Docker build fails**: Check Docker Desktop is running
3. **Firebase connection fails**: Verify service account keys are correct
4. **Environment not detected**: Check environment variables are set correctly

### Debug Commands

```bash
# Check environment variables
env | grep -E "(ENVIRONMENT|FIREBASE|VERCEL|RENDER)"

# Check Docker status
docker ps
docker logs <container_name>

# Check Firebase connection
python -c "from app.services.firebase_service import FirebaseService; print('OK')"
```

## Next Steps

After successful Phase 1 testing:
1. Deploy to development environment (Vercel + Render)
2. Test deployed environment functionality
3. Proceed to Phase 2: Multi-tenant authentication system
