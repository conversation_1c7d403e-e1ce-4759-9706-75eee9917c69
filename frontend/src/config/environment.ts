// Environment detection and configuration loading
import devConfig from './dev';
import demoConfig from './demo';
import prodConfig from './prod';

export type Environment = 'development' | 'demo' | 'production';

export interface EnvironmentConfig {
  firebase: {
    projectId: string;
    apiKey: string;
    authDomain: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId: string;
  };
  api: {
    baseUrl: string;
  };
  environment: Environment;
  debug: boolean;
}

/**
 * Detect the current environment based on various indicators
 */
export function detectEnvironment(): Environment {
  // Check for explicit environment variable first
  if (process.env.NEXT_PUBLIC_ENVIRONMENT) {
    return process.env.NEXT_PUBLIC_ENVIRONMENT as Environment;
  }

  // Check for Vercel environment
  if (process.env.VERCEL_ENV) {
    if (process.env.VERCEL_ENV === 'production') {
      // Check for demo deployment first (more specific)
      if (process.env.VERCEL_URL?.includes('demo')) {
        return 'demo';
      }
      // Check if it's the main production domain
      if (process.env.VERCEL_URL?.includes('www.recruiva.ai') ||
          (process.env.VERCEL_URL?.includes('recruiva.ai') && !process.env.VERCEL_URL?.includes('demo'))) {
        return 'production';
      }
      // Default production deployments to demo for safety
      return 'demo';
    }
    if (process.env.VERCEL_ENV === 'preview') {
      // Check if it's a demo preview
      if (process.env.VERCEL_GIT_COMMIT_REF?.includes('demo')) {
        return 'demo';
      }
      return 'development';
    }
    return 'development';
  }

  // Check NODE_ENV
  if (process.env.NODE_ENV === 'production') {
    // In production build, check hostname if available
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      if (hostname.includes('recruiva.ai')) {
        return 'production';
      }
      if (hostname.includes('demo')) {
        return 'demo';
      }
    }
    return 'production';
  }

  // Default to development
  return 'development';
}

/**
 * Get the configuration for the current environment
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const environment = detectEnvironment();

  switch (environment) {
    case 'development':
      return devConfig;
    case 'demo':
      return demoConfig;
    case 'production':
      return prodConfig;
    default:
      console.warn(`Unknown environment: ${environment}, falling back to development`);
      return devConfig;
  }
}

// Export the current configuration
export const config = getEnvironmentConfig();
export const currentEnvironment = detectEnvironment();

// Log the detected environment (only in development)
if (typeof window !== 'undefined' && config.debug) {
  console.log('Environment detected:', currentEnvironment);
  console.log('Configuration loaded:', {
    ...config,
    firebase: {
      ...config.firebase,
      apiKey: config.firebase.apiKey ? '***masked***' : undefined
    }
  });
}
