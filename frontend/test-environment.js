#!/usr/bin/env node
/**
 * Test script to verify frontend environment detection and configuration loading.
 * Run with: node test-environment.js
 */

// Mock Next.js environment
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

// Mock some environment variables for testing
const testCases = [
  {
    name: "Development (default)",
    env: {
      NODE_ENV: 'development'
    },
    expected: 'development'
  },
  {
    name: "Development (explicit)",
    env: {
      NODE_ENV: 'development',
      NEXT_PUBLIC_ENVIRONMENT: 'development'
    },
    expected: 'development'
  },
  {
    name: "Demo (explicit)",
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_ENVIRONMENT: 'demo'
    },
    expected: 'demo'
  },
  {
    name: "Production (explicit)",
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_ENVIRONMENT: 'production'
    },
    expected: 'production'
  },
  {
    name: "Vercel Preview (dev branch)",
    env: {
      NODE_ENV: 'production',
      VERCEL_ENV: 'preview',
      VERCEL_GIT_COMMIT_REF: 'dev'
    },
    expected: 'development'
  },
  {
    name: "Vercel Preview (demo branch)",
    env: {
      NODE_ENV: 'production',
      VERCEL_ENV: 'preview',
      VERCEL_GIT_COMMIT_REF: 'demo'
    },
    expected: 'demo'
  },
  {
    name: "Vercel Production (main domain)",
    env: {
      NODE_ENV: 'production',
      VERCEL_ENV: 'production',
      VERCEL_URL: 'www.recruiva.ai'
    },
    expected: 'production'
  },
  {
    name: "Vercel Production (demo domain)",
    env: {
      NODE_ENV: 'production',
      VERCEL_ENV: 'production',
      VERCEL_URL: 'demo.recruiva.ai'
    },
    expected: 'demo'
  }
];

function runTests() {
  console.log('=== Frontend Environment Detection Tests ===\n');
  
  let passed = 0;
  let failed = 0;
  
  for (const testCase of testCases) {
    // Save original environment
    const originalEnv = { ...process.env };
    
    // Clear relevant environment variables
    delete process.env.NEXT_PUBLIC_ENVIRONMENT;
    delete process.env.VERCEL_ENV;
    delete process.env.VERCEL_URL;
    delete process.env.VERCEL_GIT_COMMIT_REF;
    
    // Set test environment
    Object.assign(process.env, testCase.env);
    
    try {
      // Clear require cache to force re-evaluation
      const configPath = require.resolve('./src/config/environment.ts');
      delete require.cache[configPath];
      
      // Note: This is a simplified test since we can't easily test TypeScript modules
      // In a real scenario, you'd use a proper test framework like Jest
      console.log(`Testing: ${testCase.name}`);
      console.log(`  Environment variables:`, testCase.env);
      console.log(`  Expected: ${testCase.expected}`);
      console.log(`  Status: ⚠️  Manual verification needed (TypeScript module)`);
      console.log('');
      
      passed++;
    } catch (error) {
      console.log(`✗ ${testCase.name}: ${error.message}`);
      failed++;
    }
    
    // Restore original environment
    process.env = originalEnv;
  }
  
  console.log(`\n=== Test Summary ===`);
  console.log(`Total tests: ${testCases.length}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  
  if (failed === 0) {
    console.log('✓ All tests completed (manual verification needed for TypeScript)');
  } else {
    console.log('✗ Some tests failed');
    process.exit(1);
  }
}

if (require.main === module) {
  runTests();
}
