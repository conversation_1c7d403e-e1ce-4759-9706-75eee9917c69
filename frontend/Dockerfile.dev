# Development Dockerfile for Next.js with hot reload
FROM node:22-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies with legacy peer deps to resolve conflicts
RUN npm ci --legacy-peer-deps

# Expose port
EXPOSE 3000

# Set environment variables for development
ENV NODE_ENV=development
ENV NEXT_PUBLIC_ENVIRONMENT=development

# Command to run the development server
CMD ["npm", "run", "dev"]
