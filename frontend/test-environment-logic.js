#!/usr/bin/env node
/**
 * Test script to verify frontend environment detection logic.
 * This tests the actual logic without TypeScript compilation.
 */

// Environment detection logic (JavaScript version of the TypeScript code)
function detectEnvironment() {
    // Check for explicit environment variable first
    if (process.env.NEXT_PUBLIC_ENVIRONMENT) {
        return process.env.NEXT_PUBLIC_ENVIRONMENT;
    }

    // Check for Vercel environment
    if (process.env.VERCEL_ENV) {
        if (process.env.VERCEL_ENV === 'production') {
            // Check for demo deployment first (more specific)
            if (process.env.VERCEL_URL?.includes('demo')) {
                return 'demo';
            }
            // Check if it's the main production domain
            if (process.env.VERCEL_URL?.includes('www.recruiva.ai') ||
                (process.env.VERCEL_URL?.includes('recruiva.ai') && !process.env.VERCEL_URL?.includes('demo'))) {
                return 'production';
            }
            // Default production deployments to demo for safety
            return 'demo';
        }
        if (process.env.VERCEL_ENV === 'preview') {
            // Check if it's a demo preview
            if (process.env.VERCEL_GIT_COMMIT_REF?.includes('demo')) {
                return 'demo';
            }
            return 'development';
        }
        return 'development';
    }

    // Check NODE_ENV
    if (process.env.NODE_ENV === 'production') {
        // In production build, default to production
        return 'production';
    }

    // Default to development
    return 'development';
}

// Test cases
const testCases = [
    {
        name: "Development (default)",
        env: {
            NODE_ENV: 'development'
        },
        expected: 'development'
    },
    {
        name: "Development (explicit)",
        env: {
            NODE_ENV: 'development',
            NEXT_PUBLIC_ENVIRONMENT: 'development'
        },
        expected: 'development'
    },
    {
        name: "Demo (explicit)",
        env: {
            NODE_ENV: 'production',
            NEXT_PUBLIC_ENVIRONMENT: 'demo'
        },
        expected: 'demo'
    },
    {
        name: "Production (explicit)",
        env: {
            NODE_ENV: 'production',
            NEXT_PUBLIC_ENVIRONMENT: 'production'
        },
        expected: 'production'
    },
    {
        name: "Vercel Preview (dev branch)",
        env: {
            NODE_ENV: 'production',
            VERCEL_ENV: 'preview',
            VERCEL_GIT_COMMIT_REF: 'dev'
        },
        expected: 'development'
    },
    {
        name: "Vercel Preview (demo branch)",
        env: {
            NODE_ENV: 'production',
            VERCEL_ENV: 'preview',
            VERCEL_GIT_COMMIT_REF: 'demo'
        },
        expected: 'demo'
    },
    {
        name: "Vercel Production (main domain)",
        env: {
            NODE_ENV: 'production',
            VERCEL_ENV: 'production',
            VERCEL_URL: 'www.recruiva.ai'
        },
        expected: 'production'
    },
    {
        name: "Vercel Production (demo domain)",
        env: {
            NODE_ENV: 'production',
            VERCEL_ENV: 'production',
            VERCEL_URL: 'demo.recruiva.ai'
        },
        expected: 'demo'
    },
    {
        name: "Vercel Production (dev domain)",
        env: {
            NODE_ENV: 'production',
            VERCEL_ENV: 'production',
            VERCEL_URL: 'recruiva-dev.vercel.app'
        },
        expected: 'demo'
    },
    {
        name: "Production build without Vercel",
        env: {
            NODE_ENV: 'production'
        },
        expected: 'production'
    }
];

function runTests() {
    console.log('=== Frontend Environment Detection Logic Tests ===\n');

    let passed = 0;
    let failed = 0;

    for (const testCase of testCases) {
        // Save original environment
        const originalEnv = { ...process.env };

        // Clear relevant environment variables
        delete process.env.NEXT_PUBLIC_ENVIRONMENT;
        delete process.env.VERCEL_ENV;
        delete process.env.VERCEL_URL;
        delete process.env.VERCEL_GIT_COMMIT_REF;
        delete process.env.NODE_ENV;

        // Set test environment
        Object.assign(process.env, testCase.env);

        try {
            const result = detectEnvironment();
            const success = result === testCase.expected;

            if (success) {
                console.log(`✓ ${testCase.name}`);
                console.log(`  Expected: ${testCase.expected}, Got: ${result}`);
                passed++;
            } else {
                console.log(`✗ ${testCase.name}`);
                console.log(`  Expected: ${testCase.expected}, Got: ${result}`);
                console.log(`  Environment:`, testCase.env);
                failed++;
            }
            console.log('');
        } catch (error) {
            console.log(`✗ ${testCase.name}: ${error.message}`);
            failed++;
        }

        // Restore original environment
        process.env = originalEnv;
    }

    console.log(`=== Test Summary ===`);
    console.log(`Total tests: ${testCases.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);

    if (failed === 0) {
        console.log('✓ All frontend environment detection tests passed!');
        return true;
    } else {
        console.log('✗ Some tests failed');
        return false;
    }
}

if (require.main === module) {
    const success = runTests();
    process.exit(success ? 0 : 1);
}

module.exports = { detectEnvironment, runTests };
